/**
 * Integration Test Setup
 * Sets up real Next.js server and PostgreSQL database for integration testing
 */

import { createServer } from 'http'
import { parse } from 'url'
import next from 'next'
import { query } from '@/lib/local-db'

// Test environment configuration
const TEST_PORT = 3001
const TEST_DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL || 'postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens'

// Global server instance for integration testing
let server: any = null
let app: any = null

/**
 * Start the Next.js server for integration testing
 */
export async function startTestServer() {
  if (server) {
    console.log('🧪 Test server already running')
    return `http://localhost:${TEST_PORT}`
  }

  console.log('🧪 Starting real Next.js server for integration testing...')

  // Set test environment variables
  if (!process.env.NODE_ENV) {
    (process.env as any).NODE_ENV = 'test'
  }
  process.env.DATABASE_URL = TEST_DATABASE_URL
  process.env.CACHE_TYPE = 'postgresql'
  process.env.SESSION_SECRET = 'test-session-secret-for-integration-tests'
  process.env.USE_LOCAL_AUTH = 'true'
  process.env.NEXT_PUBLIC_APP_URL = `http://localhost:${TEST_PORT}`

  // Create Next.js app in development mode for integration tests
  // This avoids routing manifest issues with production builds
  app = next({
    dev: true,
    dir: process.cwd(),
    port: TEST_PORT
  })

  const handle = app.getRequestHandler()

  console.log('🧪 Preparing Next.js app...')
  await app.prepare()

  // Create HTTP server
  server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url!, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('❌ Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })

  // Start server
  console.log(`🧪 Starting server on port ${TEST_PORT}...`)
  await new Promise<void>((resolve, reject) => {
    server.listen(TEST_PORT, (err?: any) => {
      if (err) {
        console.error('❌ Failed to start test server:', err)
        reject(err)
      } else {
        console.log(`✅ Test server ready on http://localhost:${TEST_PORT}`)
        resolve()
      }
    })
  })

  return `http://localhost:${TEST_PORT}`
}

/**
 * Stop the test server
 */
export async function stopTestServer() {
  if (server) {
    console.log('🧪 Stopping test server...')
    await new Promise<void>((resolve) => {
      server.close(() => {
        console.log('✅ Test server stopped')
        resolve()
      })
    })
    server = null
  }

  if (app) {
    console.log('🧪 Closing Next.js app...')
    await app.close()
    app = null
    console.log('✅ Next.js app closed')
  }
}

/**
 * Set up test database with clean state
 */
export async function setupTestDatabase() {
  try {
    console.log('🧪 Setting up test database with real PostgreSQL connection...')

    // Test database connection first
    await testDatabaseConnection()

    // Clean up existing test data
    await cleanupTestDatabase()

    // Insert comprehensive test data
    await seedTestDatabase()

    console.log('🧪 Test database setup complete')
  } catch (error) {
    console.error('❌ Test database setup failed:', error instanceof Error ? error.message : String(error))
    throw error // Throw error to fail tests if database is not available
  }
}

/**
 * Test database connection
 */
async function testDatabaseConnection() {
  try {
    const result = await query('SELECT NOW() as current_time, version() as pg_version')
    console.log('🧪 Database connection successful:', {
      time: result.rows[0].current_time,
      version: result.rows[0].pg_version.split(' ')[0]
    })
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    throw new Error('Cannot connect to PostgreSQL database. Make sure Docker is running with: npm run dev:start')
  }
}

/**
 * Clean up test database
 */
export async function cleanupTestDatabase() {
  try {
    // Define test IDs for aggressive cleanup
    const testUserIds = [
      '*************-3030-3030-************',
      '*************-4040-4040-************',
      '*************-5050-5050-************',
      '*************-6060-6060-************',
      '*************-7070-7070-************',
      '*************-8080-8080-************',
      '*************-9090-9090-************'
    ]

    const testCompanyIds = [
      'dddddddd-dddd-dddd-dddd-dddddddddddd',
      'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
      'ffffffff-ffff-ffff-ffff-ffffffffffff',
      '10101010-1010-1010-1010-101010101010',
      '*************-2020-2020-************'
    ]

    // Delete test data in correct order (respecting foreign keys)
    // Use simple LIKE patterns instead of ANY() for better compatibility
    const cleanupQueries = [
      // Clean up user-related data first
      `DELETE FROM user_benefit_rankings WHERE user_id IN ('${testUserIds.join("','")}')`,
      `DELETE FROM benefit_removal_disputes WHERE user_id IN ('${testUserIds.join("','")}')`,
      `DELETE FROM user_sessions WHERE user_id IN ('${testUserIds.join("','")}') OR session_token LIKE 'test-session-%'`,
      `DELETE FROM company_page_views WHERE user_id IN ('${testUserIds.join("','")}') OR session_id LIKE 'test-%' OR session_id LIKE 'anonymous-%'`,
      `DELETE FROM saved_companies WHERE user_id IN ('${testUserIds.join("','")}')`,
      `DELETE FROM missing_company_reports WHERE user_email LIKE '%@test%' OR user_email LIKE '%.test' OR user_email LIKE '%test.example'`,

      // Clean up company-related data
      `DELETE FROM company_benefits WHERE company_id IN ('${testCompanyIds.join("','")}')`,
      `DELETE FROM company_locations WHERE company_id IN ('${testCompanyIds.join("','")}')`,

      // Clean up users and companies
      `DELETE FROM users WHERE id IN ('${testUserIds.join("','")}') OR email LIKE '%@test%' OR email LIKE '%.test' OR email LIKE '%test.example'`,
      `DELETE FROM companies WHERE id IN ('${testCompanyIds.join("','")}') OR domain LIKE '%.test'`,

      // Clean up benefits and categories
      `DELETE FROM benefits WHERE name LIKE 'Test %'`,
      `DELETE FROM benefit_categories WHERE name LIKE 'test_%'`,
    ]

    for (const sql of cleanupQueries) {
      try {
        await query(sql)
      } catch (error) {
        // Continue with other cleanup queries even if one fails
        console.warn(`⚠️ Cleanup query failed: ${sql}`, error)
      }
    }

    console.log('🧪 Test database cleaned')
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error)
    // Don't throw - cleanup failures shouldn't break tests
  }
}

/**
 * Seed test database with comprehensive test data
 */
export async function seedTestDatabase() {
  try {
    console.log('🧪 Seeding comprehensive test data...')

    // Create test benefit categories with predictable UUIDs
    const testCategoryIds = {
      'test_health': '11111111-1111-1111-1111-111111111111',
      'test_flexibility': '*************-2222-2222-************',
      'test_financial': '*************-3333-3333-************',
      'test_wellness': '*************-4444-4444-************'
    }

    // First, try to get existing categories
    let categoryResult = await query(`
      SELECT id, name FROM benefit_categories
      WHERE id IN ($1, $2, $3, $4)
    `, [
      testCategoryIds['test_health'],
      testCategoryIds['test_flexibility'],
      testCategoryIds['test_financial'],
      testCategoryIds['test_wellness']
    ])

    // If categories don't exist, create them with specific UUIDs
    if (categoryResult.rows.length === 0) {
      categoryResult = await query(`
        INSERT INTO benefit_categories (id, name, display_name, icon)
        VALUES
          ($1, 'test_health', 'Test Health', '🏥'),
          ($2, 'test_flexibility', 'Test Flexibility', '⏰'),
          ($3, 'test_financial', 'Test Financial', '💰'),
          ($4, 'test_wellness', 'Test Wellness', '🧘')

        RETURNING id, name
      `, [
        testCategoryIds['test_health'],
        testCategoryIds['test_flexibility'],
        testCategoryIds['test_financial'],
        testCategoryIds['test_wellness']
      ])
    }

    // Store category IDs for later use
    const categoryIds: Record<string, string> = testCategoryIds

    // Create comprehensive test benefits with predictable UUIDs
    const testBenefitIds = {
      'Test Health Insurance': '*************-5555-5555-************',
      'Test Remote Work': '*************-6666-6666-************',
      'Test Dental Coverage': '*************-7777-7777-************',
      'Test Stock Options': '*************-8888-8888-************',
      'Test Gym Membership': '*************-9999-9999-************',
      'Test Mental Health Support': 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      'Test Flexible Hours': 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
      'Test Pension Plan': 'cccccccc-cccc-cccc-cccc-cccccccccccc'
    }

    // First, try to get existing benefits
    let benefitResult = await query(`
      SELECT id, name FROM benefits
      WHERE id IN ($1, $2, $3, $4, $5, $6, $7, $8)
    `, Object.values(testBenefitIds))

    // If benefits don't exist, create them with specific UUIDs
    if (benefitResult.rows.length === 0) {
      benefitResult = await query(`
        INSERT INTO benefits (id, name, description, category_id, icon)
        VALUES
          ($1, 'Test Health Insurance', 'Comprehensive health coverage for testing', $9, '🏥'),
          ($2, 'Test Remote Work', 'Flexible remote work policy for testing', $10, '🏠'),
          ($3, 'Test Dental Coverage', 'Dental insurance for testing', $9, '🦷'),
          ($4, 'Test Stock Options', 'Employee stock options for testing', $11, '📈'),
          ($5, 'Test Gym Membership', 'Fitness center membership for testing', $12, '💪'),
          ($6, 'Test Mental Health Support', 'Mental health counseling for testing', $12, '🧠'),
          ($7, 'Test Flexible Hours', 'Flexible working hours for testing', $10, '⏰'),
          ($8, 'Test Pension Plan', 'Retirement pension plan for testing', $11, '🏦')

        RETURNING id, name
      `, [
        testBenefitIds['Test Health Insurance'],
        testBenefitIds['Test Remote Work'],
        testBenefitIds['Test Dental Coverage'],
        testBenefitIds['Test Stock Options'],
        testBenefitIds['Test Gym Membership'],
        testBenefitIds['Test Mental Health Support'],
        testBenefitIds['Test Flexible Hours'],
        testBenefitIds['Test Pension Plan'],
        categoryIds['test_health'],
        categoryIds['test_flexibility'],
        categoryIds['test_financial'],
        categoryIds['test_wellness']
      ])
    }

    // Store benefit IDs for later use
    const benefitIds: Record<string, string> = testBenefitIds

    // Create comprehensive test companies with predictable UUIDs
    const testCompanyIds = {
      'testcorp.test': 'dddddddd-dddd-dddd-dddd-dddddddddddd',
      'testind.test': 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
      'startup.test': 'ffffffff-ffff-ffff-ffff-ffffffffffff',
      'consulting.test': '10101010-1010-1010-1010-101010101010',
      'finance.test': '*************-2020-2020-************'
    }

    // First, try to get existing companies
    let companyResult = await query(`
      SELECT id, name, domain FROM companies
      WHERE id IN ($1, $2, $3, $4, $5)
    `, Object.values(testCompanyIds))

    // If companies don't exist, create them with specific UUIDs
    if (companyResult.rows.length === 0) {
      companyResult = await query(`
        INSERT INTO companies (id, name, domain, industry, size, description)
        VALUES
          ($1, 'Test Corp', 'testcorp.test', 'Technology', 'medium', 'Leading technology company for testing'),
          ($2, 'Test Industries', 'testind.test', 'Manufacturing', 'large', 'Manufacturing company for testing'),
          ($3, 'Test Startup', 'startup.test', 'Technology', 'startup', 'Innovative startup for testing'),
          ($4, 'Test Consulting', 'consulting.test', 'Consulting', 'small', 'Consulting firm for testing'),
          ($5, 'Test Finance', 'finance.test', 'Finance', 'enterprise', 'Financial services company for testing')

        RETURNING id, name, domain
      `, Object.values(testCompanyIds))
    }

    // Store company IDs for later use
    const companyIds: Record<string, string> = testCompanyIds

    // Create comprehensive test users with predictable UUIDs
    const testUserIds = {
      '<EMAIL>': '*************-3030-3030-************',
      '<EMAIL>': '*************-4040-4040-************',
      '<EMAIL>': '*************-5050-5050-************',
      '<EMAIL>': '*************-6060-6060-************',
      '<EMAIL>': '*************-7070-7070-************',
      '<EMAIL>': '*************-8080-8080-************',
      '<EMAIL>': '*************-9090-9090-************'
    }

    // First, try to get existing users
    let userResult = await query(`
      SELECT id, email FROM users
      WHERE id IN ($1, $2, $3, $4, $5, $6, $7)
    `, Object.values(testUserIds))

    // If users don't exist, create them with specific UUIDs
    if (userResult.rows.length === 0) {
      userResult = await query(`
        INSERT INTO users (id, email, first_name, last_name, role, payment_status, company_id)
        VALUES
          ($1, '<EMAIL>', 'John', 'Doe', 'user', 'free', $8),
          ($2, '<EMAIL>', 'Jane', 'Smith', 'user', 'paying', $9),
          ($3, '<EMAIL>', 'Bob', 'Johnson', 'user', 'free', $10),
          ($4, '<EMAIL>', 'Alice', 'Brown', 'user', 'paying', $11),
          ($5, '<EMAIL>', 'Charlie', 'Wilson', 'user', 'free', $12),
          ($6, '<EMAIL>', 'Admin', 'User', 'admin', 'paying', null),
          ($7, '<EMAIL>', 'Super', 'Admin', 'admin', 'paying', null)

        RETURNING id, email
      `, [
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        testUserIds['<EMAIL>'],
        companyIds['testcorp.test'],
        companyIds['testind.test'],
        companyIds['startup.test'],
        companyIds['consulting.test'],
        companyIds['finance.test']
      ])
    }

    // Store user IDs for later use
    const userIds: Record<string, string> = {}
    for (const row of userResult.rows) {
      userIds[row.email] = row.id
    }

    // Create comprehensive test company benefits (with error handling)
    try {
      await query(`
        INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
        VALUES
          ($1, $2, true, $3),
          ($1, $4, true, $3),
          ($1, $5, true, $3),
          ($6, $2, true, $3),
          ($6, $7, false, null),
          ($6, $8, true, $3),
          ($9, $4, false, null),
          ($9, $10, false, null),
          ($11, $12, true, $3),
          ($13, $14, true, $3)
        ON CONFLICT (company_id, benefit_id) DO UPDATE SET is_verified = EXCLUDED.is_verified
      `, [
        companyIds['testcorp.test'], benefitIds['Test Health Insurance'], userIds['<EMAIL>'],
        benefitIds['Test Remote Work'], benefitIds['Test Stock Options'],
        companyIds['testind.test'], benefitIds['Test Dental Coverage'], benefitIds['Test Gym Membership'],
        companyIds['startup.test'], benefitIds['Test Flexible Hours'],
        companyIds['consulting.test'], benefitIds['Test Mental Health Support'],
        companyIds['finance.test'], benefitIds['Test Pension Plan']
      ])
    } catch (error) {
      console.log('⚠️ Company benefits already exist, skipping...')
    }

    // Create test company locations (with error handling)
    try {
      await query(`
        INSERT INTO company_locations (company_id, location_raw, location_normalized, city, country, country_code, is_headquarters, is_primary)
        VALUES
          ($1, 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true),
          ($1, 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', false, false),
          ($2, 'Hamburg, Germany', 'Hamburg, Germany', 'Hamburg', 'Germany', 'DE', true, true),
          ($3, 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true),
          ($4, 'Cologne, Germany', 'Cologne, Germany', 'Cologne', 'Germany', 'DE', true, true),
          ($5, 'Stuttgart, Germany', 'Stuttgart, Germany', 'Stuttgart', 'Germany', 'DE', true, true)
        ON CONFLICT (company_id, location_normalized) DO UPDATE SET is_primary = EXCLUDED.is_primary
      `, [
        companyIds['testcorp.test'],
        companyIds['testind.test'],
        companyIds['startup.test'],
        companyIds['consulting.test'],
        companyIds['finance.test']
      ])
    } catch (error) {
      console.log('⚠️ Company locations already exist, skipping...')
    }

    // Create test user benefit rankings (with error handling)
    try {
      await query(`
        INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
        VALUES
          ($1, $2, 1),
          ($1, $3, 2),
          ($1, $4, 3),
          ($5, $2, 1),
          ($5, $6, 2),
          ($5, $7, 3),
          ($8, $9, 1),
          ($8, $10, 2)
        ON CONFLICT (user_id, benefit_id) DO UPDATE SET ranking = EXCLUDED.ranking
      `, [
        userIds['<EMAIL>'], benefitIds['Test Health Insurance'], benefitIds['Test Remote Work'], benefitIds['Test Stock Options'],
        userIds['<EMAIL>'], benefitIds['Test Dental Coverage'], benefitIds['Test Gym Membership'],
        userIds['<EMAIL>'], benefitIds['Test Mental Health Support'], benefitIds['Test Flexible Hours']
      ])
    } catch (error) {
      console.log('⚠️ User benefit rankings already exist, skipping...')
    }

    // Create test saved companies (with error handling)
    try {
      await query(`
        INSERT INTO saved_companies (user_id, company_id)
        VALUES
          ($1::text, $2),
          ($1::text, $3),
          ($4::text, $5),
          ($6::text, $7)
        ON CONFLICT (user_id, company_id) DO NOTHING
      `, [
        userIds['<EMAIL>'], companyIds['testind.test'],
        companyIds['startup.test'],
        userIds['<EMAIL>'], companyIds['testcorp.test'],
        userIds['<EMAIL>'], companyIds['consulting.test']
      ])
    } catch (error) {
      console.log('⚠️ Saved companies already exist, skipping...')
    }

    // Create test missing company reports (with error handling)
    try {
      await query(`
        INSERT INTO missing_company_reports (id, user_email, email_domain, first_name, last_name, status)
        VALUES
          ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'missing.test', 'John', 'Doe', 'pending'),
          ('*************-2222-2222-************', '<EMAIL>', 'another.test', 'Jane', 'Smith', 'reviewed'),
          ('*************-3333-3333-************', '<EMAIL>', 'rejected.test', 'Bob', 'Johnson', 'rejected')
      `)
    } catch (error) {
      console.log('⚠️ Missing company reports already exist, skipping...')
    }

    // Create test company page views for analytics (with error handling)
    try {
      await query(`
        INSERT INTO company_page_views (company_id, user_id, session_id, ip_address)
        VALUES
          ($1, $2, 'test-session-1', '127.0.0.1'),
          ($3, $4, 'test-session-2', '127.0.0.1'),
          ($5, null, 'anonymous-session-1', '127.0.0.1'),
          ($1, null, 'anonymous-session-2', '127.0.0.1')
      `, [
        companyIds['testcorp.test'], userIds['<EMAIL>'],
        companyIds['testind.test'], userIds['<EMAIL>'],
        companyIds['startup.test']
      ])
    } catch (error) {
      console.log('⚠️ Company page views already exist, skipping...')
    }

    console.log('🧪 Comprehensive test database seeded with all scenarios')
  } catch (error) {
    console.error('❌ Test database seeding failed:', error)
    throw error
  }
}

/**
 * Create test user session for authenticated requests
 */
export async function createTestUserSession(userEmail: string): Promise<string> {
  // Get the actual user ID from the database
  const userResult = await query('SELECT id FROM users WHERE email = $1', [userEmail])
  if (userResult.rows.length === 0) {
    throw new Error(`User with email ${userEmail} not found`)
  }

  const userId = userResult.rows[0].id
  const sessionToken = `test-session-${userId}-${Date.now()}`
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  await query(`
    INSERT INTO user_sessions (session_token, user_id, expires_at)
    VALUES ($1, $2, $3)
    ON CONFLICT (session_token) DO UPDATE SET expires_at = $3
  `, [sessionToken, userId, expiresAt])

  return sessionToken
}

/**
 * Get CSRF token for authenticated requests
 */
export async function getCSRFToken(sessionToken: string): Promise<string> {
  const headers = {
    'Cookie': `session_token=${sessionToken}`,
    'Content-Type': 'application/json',
  }

  const response = await fetch(`http://localhost:${TEST_PORT}/api/auth/csrf`, { headers })

  if (!response.ok) {
    throw new Error(`Failed to get CSRF token: ${response.status}`)
  }

  const data = await response.json()
  return data.csrfToken
}

/**
 * Make authenticated request with test session
 */
export function createAuthHeaders(sessionToken: string, csrfToken?: string) {
  const headers: Record<string, string> = {
    'Cookie': `session_token=${sessionToken}`,
    'Content-Type': 'application/json',
  }

  if (csrfToken) {
    headers['X-CSRF-Token'] = csrfToken
  }

  return headers
}

/**
 * Create authenticated headers with CSRF token for state-changing requests
 */
export async function createAuthHeadersWithCSRF(sessionToken: string) {
  const csrfToken = await getCSRFToken(sessionToken)
  return createAuthHeaders(sessionToken, csrfToken)
}

/**
 * Wait for server to be ready
 */
export async function waitForServer(url: string, maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`${url}/api/health`)
      if (response.ok) {
        return true
      }
    } catch (error) {
      // Server not ready yet
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  throw new Error(`Server not ready after ${maxAttempts} attempts`)
}

// Global setup and teardown for integration tests
export async function globalSetup() {
  console.log('🧪 Starting integration test setup...')
  
  const serverUrl = await startTestServer()
  await waitForServer(serverUrl)
  await setupTestDatabase()
  
  // Store server URL for tests
  process.env.TEST_SERVER_URL = serverUrl
  
  console.log('✅ Integration test setup complete')
}

export async function globalTeardown() {
  console.log('🧪 Starting integration test teardown...')
  
  await cleanupTestDatabase()
  await stopTestServer()
  
  console.log('✅ Integration test teardown complete')
}
