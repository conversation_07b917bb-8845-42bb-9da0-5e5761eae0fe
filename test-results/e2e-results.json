{"config": {"configFile": "/home/<USER>/git/workwell/playwright.config.ts", "rootDir": "/home/<USER>/git/workwell/src/__tests__/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/home/<USER>/git/workwell/src/__tests__/e2e/global-setup.ts", "globalTeardown": "/home/<USER>/git/workwell/src/__tests__/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/git/workwell/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/git/workwell/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "admin-workflows.spec.ts", "file": "admin-workflows.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Workflow E2E Tests", "file": "admin-workflows.spec.ts", "line": 24, "column": 6, "specs": [{"title": "Admin Company Management Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 10966, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:31:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:31:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:26:33.705Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-d7d5a-Company-Management-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-d7d5a-Company-Management-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-d7d5a-Company-Management-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-a5ddf1bdfd0eeeb299e5", "file": "admin-workflows.spec.ts", "line": 29, "column": 3}, {"title": "Admin Benefit Management Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 10667, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:83:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:83:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:26:45.105Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-4607b-Benefit-Management-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-4607b-Benefit-Management-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-4607b-Benefit-Management-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-9effc56e35f36086987f", "file": "admin-workflows.spec.ts", "line": 81, "column": 3}, {"title": "Admin User Management Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 10734, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:126:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:126:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:26:56.193Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-8ea43-in-User-Management-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-8ea43-in-User-Management-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-8ea43-in-User-Management-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-595c432b853366165270", "file": "admin-workflows.spec.ts", "line": 124, "column": 3}, {"title": "Admin Benefit Verification Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 10723, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:172:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:172:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:27:07.366Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-5f357-nefit-Verification-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-5f357-nefit-Verification-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-5f357-nefit-Verification-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-5be28b9b385587403311", "file": "admin-workflows.spec.ts", "line": 170, "column": 3}, {"title": "Admin Analytics and Reporting Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 10656, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:206:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:206:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:27:18.511Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9d7e3-tics-and-Reporting-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9d7e3-tics-and-Reporting-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9d7e3-tics-and-Reporting-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-bb5a14f71edd189bc691", "file": "admin-workflows.spec.ts", "line": 204, "column": 3}, {"title": "Super Admin Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 10663, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:247:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:247:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:27:29.625Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-9253e--Tests-Super-Admin-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-c50c6c8d53acf60f4750", "file": "admin-workflows.spec.ts", "line": 245, "column": 3}, {"title": "Admin <PERSON>rror Handling and Security", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 10952, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:293:24", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 24, "line": 293}, "snippet": "\u001b[0m \u001b[90m 291 |\u001b[39m     \n \u001b[90m 292 |\u001b[39m     \u001b[90m// Should redirect to sign-in\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 293 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/auth\\/sign-in/\u001b[39m)\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 294 |\u001b[39m     \n \u001b[90m 295 |\u001b[39m     \u001b[90m// 2. Sign in as regular user\u001b[39m\n \u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'user1@techcorp.e2e'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 24, "line": 293}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 291 |\u001b[39m     \n \u001b[90m 292 |\u001b[39m     \u001b[90m// Should redirect to sign-in\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 293 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/auth\\/sign-in/\u001b[39m)\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 294 |\u001b[39m     \n \u001b[90m 295 |\u001b[39m     \u001b[90m// 2. Sign in as regular user\u001b[39m\n \u001b[90m 296 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'user1@techcorp.e2e'\u001b[39m)\u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:293:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:27:40.721Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-498c7-Error-Handling-and-Security-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-498c7-Error-Handling-and-Security-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-498c7-Error-Handling-and-Security-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 24, "line": 293}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-bb97959b230dff69ae2e", "file": "admin-workflows.spec.ts", "line": 288, "column": 3}, {"title": "Admin Bulk Operations Workflow", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "failed", "duration": 10674, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:327:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInAdmin(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// Mock admin verification\u001b[39m\u001b[0m\n\u001b[2m    at signInAdmin (/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts:327:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:27:52.083Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-fc7cc-in-Bulk-Operations-Workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-fc7cc-in-Bulk-Operations-Workflow-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-workflows-Admin-Work-fc7cc-in-Bulk-Operations-Workflow-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/admin-workflows.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "0a039903b3d08eb2556f-0b14cfb40192b1e4f365", "file": "admin-workflows.spec.ts", "line": 325, "column": 3}]}]}, {"title": "user-journeys.spec.ts", "file": "user-journeys.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Critical User Journeys", "file": "user-journeys.spec.ts", "line": 27, "column": 6, "specs": [{"title": "Complete User Registration and Company Discovery Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 13122, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:41:16", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// 2. User searches for companies\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'E2E Tech'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 44 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 41}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// 2. User searches for companies\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'E2E Tech'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 44 |\u001b[39m     \u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:41:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:28:03.164Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-f1139-d-Company-Discovery-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-f1139-d-Company-Discovery-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-f1139-d-Company-Discovery-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 41}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-54e5ed9b3d361118460e", "file": "user-journeys.spec.ts", "line": 33, "column": 3}, {"title": "User Benefit Management Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 10676, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInUser (/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:78:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// In a real app, this would involve email verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// In a real app, this would involve email verification\u001b[39m\u001b[0m\n\u001b[2m    at signInUser (/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:78:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:28:16.724Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-c1e07--Benefit-Management-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-5a9ebadc74344c5bcdde", "file": "user-journeys.spec.ts", "line": 76, "column": 3}, {"title": "User Benefit Ranking Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 10686, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n    at signInUser (/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:11:14)\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:115:5", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// In a real app, this would involve email verification\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m signInUser(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m email\u001b[33m:\u001b[39m string) {\n \u001b[90m 10 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth/sign-in'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m email)\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\n \u001b[90m 13 |\u001b[39m   \n \u001b[90m 14 |\u001b[39m   \u001b[90m// In a real app, this would involve email verification\u001b[39m\u001b[0m\n\u001b[2m    at signInUser (/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:11:14)\u001b[22m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:115:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:28:27.835Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-5ec55-ser-Benefit-Ranking-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 14, "line": 11}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-f2a25174262d5e1ff223", "file": "user-journeys.spec.ts", "line": 113, "column": 3}, {"title": "Company Search and Filter Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 12854, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"location\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"location\"]')\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:16", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 154}, "snippet": "\u001b[0m \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 2. User searches by location\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"location\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Berlin'\u001b[39m)\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"location\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 156 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 157 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 154}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"location\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 2. User searches by location\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"location\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Berlin'\u001b[39m)\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"location\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 156 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 157 |\u001b[39m     \u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:154:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:28:38.954Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-cab13-y-Search-and-Filter-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-cab13-y-Search-and-Filter-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-cab13-y-Search-and-Filter-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 154}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-4b65e730ffbb926120a5", "file": "user-journeys.spec.ts", "line": 148, "column": 3}, {"title": "Benefits Discovery Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "failed", "duration": 13203, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('text=E2E Health Insurance')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=E2E Health Insurance')\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('text=E2E Health Insurance')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=E2E Health Insurance')\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:190:61", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 61, "line": 190}, "snippet": "\u001b[0m \u001b[90m 188 |\u001b[39m     \n \u001b[90m 189 |\u001b[39m     \u001b[90m// Should see all benefits\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Health Insurance'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Remote Work'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Dental Coverage'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 193 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 61, "line": 190}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('text=E2E Health Insurance')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=E2E Health Insurance')\u001b[22m\n\n\n\u001b[0m \u001b[90m 188 |\u001b[39m     \n \u001b[90m 189 |\u001b[39m     \u001b[90m// Should see all benefits\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Health Insurance'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Remote Work'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 192 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=E2E Dental Coverage'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 193 |\u001b[39m     \u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:190:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:28:52.220Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-33a93--Benefits-Discovery-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-33a93--Benefits-Discovery-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-33a93--Benefits-Discovery-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 61, "line": 190}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-8aadec13cb978f304b98", "file": "user-journeys.spec.ts", "line": 184, "column": 3}, {"title": "Mobile User Journey", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [{"type": "skip", "description": "This test is only for mobile", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "line": 223, "column": 10}}], "expectedStatus": "skipped", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "skipped", "duration": 126, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:29:05.848Z", "annotations": [{"type": "skip", "description": "This test is only for mobile", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "line": 223, "column": 10}}], "attachments": []}], "status": "skipped"}], "id": "e47fcf35bc402c5fa352-92a1a6f5096be43d3894", "file": "user-journeys.spec.ts", "line": 222, "column": 3}, {"title": "Error <PERSON>", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 10656, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:260:24", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 24, "line": 260}, "snippet": "\u001b[0m \u001b[90m 258 |\u001b[39m     \n \u001b[90m 259 |\u001b[39m     \u001b[90m// Should redirect to sign-in\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 260 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/auth\\/sign-in/\u001b[39m)\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 261 |\u001b[39m     \n \u001b[90m 262 |\u001b[39m     \u001b[90m// 2. User enters invalid email\u001b[39m\n \u001b[90m 263 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'invalid-email'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 24, "line": 260}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveURL\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nExpected pattern: \u001b[32m/\\/auth\\/sign-in/\u001b[39m\nReceived string:  \u001b[31m\"http://localhost:3000/sign-in\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toHaveURL\" with timeout 10000ms\u001b[22m\n\u001b[2m    14 × unexpected value \"http://localhost:3000/sign-in\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 258 |\u001b[39m     \n \u001b[90m 259 |\u001b[39m     \u001b[90m// Should redirect to sign-in\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 260 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/auth\\/sign-in/\u001b[39m)\n \u001b[90m     |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 261 |\u001b[39m     \n \u001b[90m 262 |\u001b[39m     \u001b[90m// 2. User enters invalid email\u001b[39m\n \u001b[90m 263 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[type=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'invalid-email'\u001b[39m)\u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:260:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:29:06.036Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-User-Journeys-Error-Handling-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-User-Journeys-Error-Handling-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-User-Journeys-Error-Handling-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 24, "line": 260}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-1d6c9c0b8c86f4cd1afe", "file": "user-journeys.spec.ts", "line": 255, "column": 3}, {"title": "Performance and Loading Journey", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "failed", "duration": 12617, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n\n    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:292:16", "location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m     \u001b[90m// 2. Test search performance\u001b[39m\n \u001b[90m 291 |\u001b[39m     \u001b[36mconst\u001b[39m searchStart \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'E2E'\u001b[39m)\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 294 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 295 |\u001b[39m     \u001b[36mconst\u001b[39m searchTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m searchStart\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 292}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search companies\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m     \u001b[90m// 2. Test search performance\u001b[39m\n \u001b[90m 291 |\u001b[39m     \u001b[36mconst\u001b[39m searchStart \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'E2E'\u001b[39m)\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mpress(\u001b[32m'input[placeholder*=\"Search companies\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Enter'\u001b[39m)\n \u001b[90m 294 |\u001b[39m     \u001b[36mawait\u001b[39m waitForPageLoad(page)\n \u001b[90m 295 |\u001b[39m     \u001b[36mconst\u001b[39m searchTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m searchStart\u001b[0m\n\u001b[2m    at /home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts:292:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-24T10:29:17.061Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-83d2d-ormance-and-Loading-Journey-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-83d2d-ormance-and-Loading-Journey-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/user-journeys-Critical-Use-83d2d-ormance-and-Loading-Journey-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/git/workwell/src/__tests__/e2e/user-journeys.spec.ts", "column": 16, "line": 292}}], "status": "unexpected"}], "id": "e47fcf35bc402c5fa352-e89715891f7cfa873939", "file": "user-journeys.spec.ts", "line": 280, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-24T10:26:28.649Z", "duration": 181177.70599999998, "expected": 0, "skipped": 1, "unexpected": 15, "flaky": 0}}