{"timestamp": "2025-08-25T09:24:41Z", "results": {"unit": {"numTotalTestSuites": 131, "numPassedTestSuites": 131, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 236, "numPassedTests": 235, "numFailedTests": 0, "numPendingTests": 1, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1756113840585, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset all analytics data", "status": "passed", "title": "should reset all analytics data", "duration": 2.9949369999999362, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset specific analytics data type", "status": "passed", "title": "should reset specific analytics data type", "duration": 0.6378369999999904, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset analytics data by date range", "status": "passed", "title": "should reset analytics data by date range", "duration": 0.44900299999994786, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should require confirmation for reset operations", "status": "passed", "title": "should require confirmation for reset operations", "duration": 0.3290159999999105, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should handle confirmed reset operation", "status": "passed", "title": "should handle confirmed reset operation", "duration": 0.34083800000007614, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "System Analytics Dashboard"], "fullName": "Admin Analytics Management Flows System Analytics Dashboard should get comprehensive system analytics", "status": "passed", "title": "should get comprehensive system analytics", "duration": 0.4712299999998777, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "System Analytics Dashboard"], "fullName": "Admin Analytics Management Flows System Analytics Dashboard should get analytics data with date filtering", "status": "passed", "title": "should get analytics data with date filtering", "duration": 0.48433299999987867, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should get real-time system health", "status": "passed", "title": "should get real-time system health", "duration": 0.5858089999999265, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should handle system health with warnings", "status": "passed", "title": "should handle system health with warnings", "duration": 0.5482130000000325, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should get live analytics stream", "status": "passed", "title": "should get live analytics stream", "duration": 0.4603749999998854, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should export analytics data", "status": "passed", "title": "should export analytics data", "duration": 0.3081440000000839, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should get export status", "status": "passed", "title": "should get export status", "duration": 0.2609860000000026, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should create analytics backup", "status": "passed", "title": "should create analytics backup", "duration": 0.21812799999997878, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Configuration Management"], "fullName": "Admin Analytics Management Flows Analytics Configuration Management should get analytics configuration", "status": "passed", "title": "should get analytics configuration", "duration": 0.4601869999999053, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Configuration Management"], "fullName": "Admin Analytics Management Flows Analytics Configuration Management should update analytics configuration", "status": "passed", "title": "should update analytics configuration", "duration": 0.3538450000000921, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Authorization and Security"], "fullName": "Admin Analytics Management Flows Authorization and Security should require admin authorization for analytics management", "status": "passed", "title": "should require admin authorization for analytics management", "duration": 0.20655899999997018, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Authorization and Security"], "fullName": "Admin Analytics Management Flows Authorization and Security should require super admin role for reset operations", "status": "passed", "title": "should require super admin role for reset operations", "duration": 0.18418399999995927, "failureMessages": [], "meta": {}}], "startTime": 1756113841722, "endTime": 1756113841731.3538, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-analytics-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should create a new benefit", "status": "passed", "title": "should create a new benefit", "duration": 2.5989119999999275, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should validate required fields for benefit creation", "status": "passed", "title": "should validate required fields for benefit creation", "duration": 0.47077999999987696, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should handle duplicate benefit creation", "status": "passed", "title": "should handle duplicate benefit creation", "duration": 0.45389300000010735, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should update benefit information", "status": "passed", "title": "should update benefit information", "duration": 0.4276400000001104, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should activate/deactivate benefit", "status": "passed", "title": "should activate/deactivate benefit", "duration": 0.4487469999999121, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should handle non-existent benefit update", "status": "passed", "title": "should handle non-existent benefit update", "duration": 0.35545999999999367, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should delete benefit", "status": "passed", "title": "should delete benefit", "duration": 0.27090500000008433, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should handle deletion of benefit with dependencies", "status": "passed", "title": "should handle deletion of benefit with dependencies", "duration": 0.41264400000000023, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should force delete benefit with dependencies", "status": "passed", "title": "should force delete benefit with dependencies", "duration": 1.1453970000000027, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should create new benefit category", "status": "passed", "title": "should create new benefit category", "duration": 0.49913500000002387, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should update benefit category", "status": "passed", "title": "should update benefit category", "duration": 0.2975059999998848, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should delete benefit category", "status": "passed", "title": "should delete benefit category", "duration": 0.2838859999999386, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should handle deletion of category with benefits", "status": "passed", "title": "should handle deletion of category with benefits", "duration": 0.25795399999992696, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should get pending benefit verifications", "status": "passed", "title": "should get pending benefit verifications", "duration": 0.631163000000015, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should approve benefit verification", "status": "passed", "title": "should approve benefit verification", "duration": 0.5470439999999144, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should reject benefit verification", "status": "passed", "title": "should reject benefit verification", "duration": 0.25337500000000546, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Analytics and Reporting"], "fullName": "Admin Benefit Management Flows Benefit Analytics and Reporting should get benefit analytics dashboard", "status": "passed", "title": "should get benefit analytics dashboard", "duration": 0.506114000000025, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Analytics and Reporting"], "fullName": "Admin Benefit Management Flows Benefit Analytics and Reporting should export benefit data", "status": "passed", "title": "should export benefit data", "duration": 0.25230099999998856, "failureMessages": [], "meta": {}}], "startTime": 1756113841740, "endTime": 1756113841751.2522, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-benefit-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should create a new company", "status": "passed", "title": "should create a new company", "duration": 3.0892729999999347, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should validate required fields for company creation", "status": "passed", "title": "should validate required fields for company creation", "duration": 0.5005790000000161, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should handle duplicate company creation", "status": "passed", "title": "should handle duplicate company creation", "duration": 0.3311229999999341, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should update company information", "status": "passed", "title": "should update company information", "duration": 0.3676350000000639, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should verify company", "status": "passed", "title": "should verify company", "duration": 0.40541799999994055, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should handle non-existent company update", "status": "passed", "title": "should handle non-existent company update", "duration": 0.33257000000003245, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should delete company", "status": "passed", "title": "should delete company", "duration": 0.23032599999999093, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should handle deletion of company with dependencies", "status": "passed", "title": "should handle deletion of company with dependencies", "duration": 0.36735300000009374, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should force delete company with dependencies", "status": "passed", "title": "should force delete company with dependencies", "duration": 1.382816000000048, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Discovery and Notification"], "fullName": "Admin Company Management Flows Company Discovery and Notification should run discover and notify for matching domain", "status": "passed", "title": "should run discover and notify for matching domain", "duration": 0.5428460000000541, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Discovery and Notification"], "fullName": "Admin Company Management Flows Company Discovery and Notification should handle discovery with no matching users", "status": "passed", "title": "should handle discovery with no matching users", "duration": 0.40741300000001957, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Analytics and Reporting"], "fullName": "Admin Company Management Flows Company Analytics and Reporting should get company analytics dashboard", "status": "passed", "title": "should get company analytics dashboard", "duration": 0.43927100000007613, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Analytics and Reporting"], "fullName": "Admin Company Management Flows Company Analytics and Reporting should export company data", "status": "passed", "title": "should export company data", "duration": 0.29541500000004817, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Authorization and Security"], "fullName": "Admin Company Management Flows Authorization and Security should require admin authorization", "status": "passed", "title": "should require admin authorization", "duration": 0.32612200000005487, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Authorization and Security"], "fullName": "Admin Company Management Flows Authorization and Security should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.2140470000000505, "failureMessages": [], "meta": {}}], "startTime": 1756113841617, "endTime": 1756113841627.3262, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-company-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should get all users with pagination", "status": "passed", "title": "should get all users with pagination", "duration": 2.984831999999983, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should search users by email", "status": "passed", "title": "should search users by email", "duration": 0.578296000000023, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should filter users by company", "status": "passed", "title": "should filter users by company", "duration": 0.4481119999999237, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should filter users by role", "status": "passed", "title": "should filter users by role", "duration": 0.3593989999999394, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Details and Profile Management"], "fullName": "Admin User Management Flows User Details and Profile Management should get user details", "status": "passed", "title": "should get user details", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Details and Profile Management"], "fullName": "Admin User Management Flows User Details and Profile Management should handle non-existent user", "status": "passed", "title": "should handle non-existent user", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should update user information", "status": "passed", "title": "should update user information", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should deactivate user account", "status": "passed", "title": "should deactivate user account", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should reactivate user account", "status": "passed", "title": "should reactivate user account", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should delete user account", "status": "passed", "title": "should delete user account", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Company Associations"], "fullName": "Admin User Management Flows User Company Associations should update user company association", "status": "passed", "title": "should update user company association", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Company Associations"], "fullName": "Admin User Management Flows User Company Associations should remove user company association", "status": "passed", "title": "should remove user company association", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should get user analytics dashboard", "status": "passed", "title": "should get user analytics dashboard", "duration": 0.5160489999999527, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should get user activity timeline", "status": "passed", "title": "should get user activity timeline", "duration": 0.8221530000000712, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should export user data", "status": "passed", "title": "should export user data", "duration": 0.43116600000007566, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should require admin authorization", "status": "passed", "title": "should require admin authorization", "duration": 0.2719130000000405, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.17643899999995938, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should prevent admin from deleting themselves", "status": "passed", "title": "should prevent admin from deleting themselves", "duration": 0.15087400000004436, "failureMessages": [], "meta": {}}], "startTime": 1756113841616, "endTime": 1756113841627.151, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-user-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track page views without authentication", "status": "passed", "title": "should track page views without authentication", "duration": 7.7605049999999665, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track search queries", "status": "passed", "title": "should track search queries", "duration": 0.6068410000000313, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track company profile views", "status": "passed", "title": "should track company profile views", "duration": 0.3955069999999523, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Data Validation"], "fullName": "Simple Analytics System Analytics Data Validation should validate required fields in tracking data", "status": "passed", "title": "should validate required fields in tracking data", "duration": 0.524752000000035, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Data Validation"], "fullName": "Simple Analytics System Analytics Data Validation should validate tracking data format", "status": "passed", "title": "should validate tracking data format", "duration": 0.2813099999998485, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Rate Limiting"], "fullName": "Simple Analytics System Rate Limiting should handle rate limiting for analytics tracking", "status": "passed", "title": "should handle rate limiting for analytics tracking", "duration": 0.4024939999999333, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Aggregation"], "fullName": "Simple Analytics System Analytics Aggregation should aggregate daily analytics data", "status": "passed", "title": "should aggregate daily analytics data", "duration": 0.3860139999999319, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Aggregation"], "fullName": "Simple Analytics System Analytics Aggregation should provide weekly analytics trends", "status": "passed", "title": "should provide weekly analytics trends", "duration": 0.29751499999997577, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Erro<PERSON>"], "fullName": "Simple Analytics System Error Handling should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 1.8207310000000234, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Erro<PERSON>"], "fullName": "Simple Analytics System Error Handling should handle malformed JSON responses", "status": "passed", "title": "should handle malformed JSON responses", "duration": 0.37682199999994737, "failureMessages": [], "meta": {}}], "startTime": 1756113841850, "endTime": 1756113841863.3767, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics-simple.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Analytics System Validation", "Database Schema Validation"], "fullName": "Analytics System Validation Database Schema Validation should validate analytics tables exist", "status": "passed", "title": "should validate analytics tables exist", "duration": 1.6042919999999867, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Database Schema Validation"], "fullName": "Analytics System Validation Database Schema Validation should validate analytics table columns", "status": "passed", "title": "should validate analytics table columns", "duration": 0.2798939999998993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate analytics tracking endpoint", "status": "passed", "title": "should validate analytics tracking endpoint", "duration": 0.6828160000000025, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate analytics insights endpoint", "status": "passed", "title": "should validate analytics insights endpoint", "duration": 0.7652259999999842, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate admin analytics endpoints", "status": "passed", "title": "should validate admin analytics endpoints", "duration": 0.2789709999999559, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Data Integrity Validation"], "fullName": "Analytics System Validation Data Integrity Validation should validate analytics data consistency", "status": "passed", "title": "should validate analytics data consistency", "duration": 0.2589990000000171, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Data Integrity Validation"], "fullName": "Analytics System Validation Data Integrity Validation should validate analytics aggregation accuracy", "status": "passed", "title": "should validate analytics aggregation accuracy", "duration": 0.1579629999999952, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Performance Validation"], "fullName": "Analytics System Validation Performance Validation should validate analytics query performance", "status": "passed", "title": "should validate analytics query performance", "duration": 0.3783540000000585, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Performance Validation"], "fullName": "Analytics System Validation Performance Validation should validate analytics data volume handling", "status": "passed", "title": "should validate analytics data volume handling", "duration": 0.3392220000000634, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Security Validation"], "fullName": "Analytics System Validation Security Validation should validate authentication requirements", "status": "passed", "title": "should validate authentication requirements", "duration": 0.3012029999999868, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Security Validation"], "fullName": "Analytics System Validation Security Validation should validate authorization for admin endpoints", "status": "passed", "title": "should validate authorization for admin endpoints", "duration": 0.2717139999999745, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Error Handling Validation"], "fullName": "Analytics System Validation Error Handling Validation should validate graceful error handling", "status": "passed", "title": "should validate graceful error handling", "duration": 0.1883269999999584, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Error Handling Validation"], "fullName": "Analytics System Validation Error Handling Validation should validate input validation", "status": "passed", "title": "should validate input validation", "duration": 0.2444430000000466, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Integration Validation"], "fullName": "Analytics System Validation Integration Validation should validate end-to-end analytics flow", "status": "passed", "title": "should validate end-to-end analytics flow", "duration": 0.4642709999999397, "failureMessages": [], "meta": {}}], "startTime": 1756113842345, "endTime": 1756113842351.4644, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics-validation.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track company views", "status": "passed", "title": "should track company views", "duration": 3.0015469999999596, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track benefit searches", "status": "passed", "title": "should track benefit searches", "duration": 0.43979300000000876, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track user interactions", "status": "passed", "title": "should track user interactions", "duration": 0.34645499999999174, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Data Retrieval"], "fullName": "Analytics System Analytics Data Retrieval should retrieve analytics insights for paying users", "status": "passed", "title": "should retrieve analytics insights for paying users", "duration": 0.2877049999999599, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Data Retrieval"], "fullName": "Analytics System Analytics Data Retrieval should return preview data for non-paying users", "status": "passed", "title": "should return preview data for non-paying users", "duration": 0.13784899999996014, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Admin Analytics"], "fullName": "Analytics System Admin Analytics should allow admin to reset analytics data", "status": "passed", "title": "should allow admin to reset analytics data", "duration": 0.2207339999999931, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Admin Analytics"], "fullName": "Analytics System Admin Analytics should provide admin analytics dashboard data", "status": "passed", "title": "should provide admin analytics dashboard data", "duration": 0.12164099999995415, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Erro<PERSON>"], "fullName": "Analytics System Error Handling should handle invalid tracking data", "status": "passed", "title": "should handle invalid tracking data", "duration": 0.12571700000000874, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Erro<PERSON>"], "fullName": "Analytics System Error Handling should handle unauthorized access to admin endpoints", "status": "passed", "title": "should handle unauthorized access to admin endpoints", "duration": 0.*****************, "failureMessages": [], "meta": {}}], "startTime": *************, "endTime": *************.2207, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics.test.ts"}, {"assertionResults": [{"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-up"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-up should create new user account", "status": "passed", "title": "should create new user account", "duration": 2.***************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-up"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-up should validate email format", "status": "passed", "title": "should validate email format", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-in"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-in should send magic link for existing user", "status": "passed", "title": "should send magic link for existing user", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-in"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-in should handle non-existent user", "status": "passed", "title": "should handle non-existent user", "duration": 0.2245340000001761, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/magic-link"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/magic-link should verify valid magic link", "status": "passed", "title": "should verify valid magic link", "duration": 0.21539499999994405, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/magic-link"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/magic-link should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.14264400000001842, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies"], "fullName": "API Integration Tests Company Endpoints GET /api/companies should return paginated companies", "status": "passed", "title": "should return paginated companies", "duration": 0.867757999999867, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies"], "fullName": "API Integration Tests Company Endpoints GET /api/companies should filter companies by search query", "status": "passed", "title": "should filter companies by search query", "duration": 0.2090739999998732, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies/[id]"], "fullName": "API Integration Tests Company Endpoints GET /api/companies/[id] should return company details", "status": "passed", "title": "should return company details", "duration": 0.28560299999981, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies/[id]"], "fullName": "API Integration Tests Company Endpoints GET /api/companies/[id] should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 0.178557999999839, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "POST /api/companies/[id]/benefits"], "fullName": "API Integration Tests Company Endpoints POST /api/companies/[id]/benefits should add benefit to company (authenticated)", "status": "passed", "title": "should add benefit to company (authenticated)", "duration": 0.2232930000000124, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "POST /api/companies/[id]/benefits"], "fullName": "API Integration Tests Company Endpoints POST /api/companies/[id]/benefits should require authentication", "status": "passed", "title": "should require authentication", "duration": 0.1625059999998939, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits should return all benefits", "status": "passed", "title": "should return all benefits", "duration": 0.2393170000000282, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 0.3426289999999881, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits/[id]"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits/[id] should return benefit details", "status": "passed", "title": "should return benefit details", "duration": 0.16529200000013589, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/profile"], "fullName": "API Integration Tests User Endpoints GET /api/user/profile should return user profile (authenticated)", "status": "passed", "title": "should return user profile (authenticated)", "duration": 0.17843900000002577, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/profile"], "fullName": "API Integration Tests User Endpoints GET /api/user/profile should require authentication", "status": "passed", "title": "should require authentication", "duration": 0.12291099999993094, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "POST /api/user/benefit-ranking"], "fullName": "API Integration Tests User Endpoints POST /api/user/benefit-ranking should add benefit to ranking (authenticated)", "status": "passed", "title": "should add benefit to ranking (authenticated)", "duration": 0.11668099999997139, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/saved-companies"], "fullName": "API Integration Tests User Endpoints GET /api/user/saved-companies should return saved companies (authenticated)", "status": "passed", "title": "should return saved companies (authenticated)", "duration": 0.19743400000015754, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "GET /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints GET /api/admin/companies should return companies for admin", "status": "passed", "title": "should return companies for admin", "duration": 0.1802700000000641, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "GET /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints GET /api/admin/companies should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.10714699999994082, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/companies should create company (admin)", "status": "passed", "title": "should create company (admin)", "duration": 0.08352199999990262, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "DELETE /api/admin/companies/[id]"], "fullName": "API Integration Tests Admin Endpoints DELETE /api/admin/companies/[id] should delete company (admin)", "status": "passed", "title": "should delete company (admin)", "duration": 0.0766439999999875, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/analytics/reset"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/analytics/reset should reset analytics (super admin)", "status": "passed", "title": "should reset analytics (super admin)", "duration": 0.05686599999989994, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/analytics/reset"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/analytics/reset should require super admin role", "status": "passed", "title": "should require super admin role", "duration": 0.04991799999993418, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "POST /api/analytics/track"], "fullName": "API Integration Tests Analytics Endpoints POST /api/analytics/track should track analytics event", "status": "passed", "title": "should track analytics event", "duration": 0.07037400000012894, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "POST /api/analytics/track"], "fullName": "API Integration Tests Analytics Endpoints POST /api/analytics/track should validate tracking data", "status": "passed", "title": "should validate tracking data", "duration": 0.060365000000047075, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "GET /api/analytics/insights"], "fullName": "API Integration Tests Analytics Endpoints GET /api/analytics/insights should return insights for premium users", "status": "passed", "title": "should return insights for premium users", "duration": 0.0552029999998922, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "GET /api/analytics/insights"], "fullName": "API Integration Tests Analytics Endpoints GET /api/analytics/insights should return preview for non-premium users", "status": "passed", "title": "should return preview for non-premium users", "duration": 0.06667900000002192, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 404 for non-existent endpoints", "status": "passed", "title": "should handle 404 for non-existent endpoints", "duration": 0.052989000000025044, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 405 for unsupported methods", "status": "passed", "title": "should handle 405 for unsupported methods", "duration": 0.04896799999983159, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 500 for server errors", "status": "passed", "title": "should handle 500 for server errors", "duration": 0.04469199999994089, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.08338800000001356, "failureMessages": [], "meta": {}}], "startTime": 1756113841743, "endTime": 1756113841752.0835, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/api-integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Database Location Functions", "getCompanyLocations"], "fullName": "Database Location Functions getCompanyLocations should fetch company locations ordered correctly", "status": "passed", "title": "should fetch company locations ordered correctly", "duration": 4.757614999999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "getCompanyLocations"], "fullName": "Database Location Functions getCompanyLocations should return empty array for company with no locations", "status": "passed", "title": "should return empty array for company with no locations", "duration": 0.4480220000000372, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should add a new company location", "status": "passed", "title": "should add a new company location", "duration": 1.5942339999999149, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should unset other primary locations when setting as primary", "status": "passed", "title": "should unset other primary locations when setting as primary", "duration": 0.6369709999999031, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should unset other headquarters when setting as headquarters", "status": "passed", "title": "should unset other headquarters when setting as headquarters", "duration": 0.5300139999999374, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "updateCompanyLocation"], "fullName": "Database Location Functions updateCompanyLocation should update location with new data", "status": "passed", "title": "should update location with new data", "duration": 13.055243000000019, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "updateCompanyLocation"], "fullName": "Database Location Functions updateCompanyLocation should throw error for non-existent location", "status": "passed", "title": "should throw error for non-existent location", "duration": 2.1145239999998466, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "removeCompanyLocation"], "fullName": "Database Location Functions removeCompanyLocation should remove company location", "status": "passed", "title": "should remove company location", "duration": 2.7286059999998997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "removeCompanyLocation"], "fullName": "Database Location Functions removeCompanyLocation should throw error for non-existent location", "status": "passed", "title": "should throw error for non-existent location", "duration": 0.4743029999999635, "failureMessages": [], "meta": {}}], "startTime": 1756113841783, "endTime": 1756113841810.4744, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/database-locations.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should normalize German city names", "status": "passed", "title": "should normalize German city names", "duration": 127.51800400000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should normalize English city names", "status": "passed", "title": "should normalize English city names", "duration": 12.160276999999951, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle cities with country already specified", "status": "passed", "title": "should handle cities with country already specified", "duration": 11.409395000000131, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should use database mapping when available", "status": "skipped", "title": "should use database mapping when available", "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle empty location", "status": "passed", "title": "should handle empty location", "duration": 1.9744269999998778, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should detect German cities by patterns", "status": "passed", "title": "should detect German cities by patterns", "duration": 29.55296199999998, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle international cities", "status": "passed", "title": "should handle international cities", "duration": 11.648898999999801, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle unknown locations gracefully", "status": "passed", "title": "should handle unknown locations gracefully", "duration": 24.93174799999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return empty array for short queries", "status": "passed", "title": "should return empty array for short queries", "duration": 0.768777, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return database suggestions when available", "status": "passed", "title": "should return database suggestions when available", "duration": 0.44029999999997926, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return database suggestions with correct format", "status": "passed", "title": "should return database suggestions with correct format", "duration": 0.3460850000001301, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should limit results correctly", "status": "passed", "title": "should limit results correctly", "duration": 0.17549299999996038, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should avoid duplicates", "status": "passed", "title": "should avoid duplicates", "duration": 0.17509599999993952, "failureMessages": [], "meta": {}}], "startTime": 1756113842010, "endTime": 1756113842232.175, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/location-normalization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should set cache successfully", "status": "passed", "title": "should set cache successfully", "duration": 4.476240999999845, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should get cache successfully", "status": "passed", "title": "should get cache successfully", "duration": 0.5375920000001315, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should return null for non-existent cache", "status": "passed", "title": "should return null for non-existent cache", "duration": 0.24522200000001249, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should delete cache successfully", "status": "passed", "title": "should delete cache successfully", "duration": 0.37073400000008405, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should clear cache pattern successfully", "status": "passed", "title": "should clear cache pattern successfully", "duration": 0.41367300000001705, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should set session successfully", "status": "passed", "title": "should set session successfully", "duration": 3.2695689999998194, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should get session successfully", "status": "passed", "title": "should get session successfully", "duration": 0.4326020000000881, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should delete session successfully", "status": "passed", "title": "should delete session successfully", "duration": 0.9014639999998053, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "CSRF Token Management"], "fullName": "PostgreSQL Cache System CSRF Token Management should set CSRF token successfully", "status": "passed", "title": "should set CSRF token successfully", "duration": 0.489467999999988, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "CSRF Token Management"], "fullName": "PostgreSQL Cache System CSRF Token Management should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.39346199999999953, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Health and Stats"], "fullName": "PostgreSQL Cache System Health and Stats should check cache health successfully", "status": "passed", "title": "should check cache health successfully", "duration": 0.40931000000000495, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Health and Stats"], "fullName": "PostgreSQL Cache System Health and Stats should get cache stats successfully", "status": "passed", "title": "should get cache stats successfully", "duration": 0.24700999999981832, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle cache set errors gracefully", "status": "passed", "title": "should handle cache set errors gracefully", "duration": 4.4197009999998045, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle cache get errors gracefully", "status": "passed", "title": "should handle cache get errors gracefully", "duration": 1.1874270000000706, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle health check errors gracefully", "status": "passed", "title": "should handle health check errors gracefully", "duration": 0.783907999999883, "failureMessages": [], "meta": {}}], "startTime": 1756113841787, "endTime": 1756113841805.784, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/postgresql-cache.test.ts"}, {"assertionResults": [{"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should allow request when under limit", "status": "passed", "title": "should allow request when under limit", "duration": 1.9565419999998994, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should deny request when over limit", "status": "passed", "title": "should deny request when over limit", "duration": 0.5762930000000779, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should handle database errors gracefully", "status": "passed", "title": "should handle database errors gracefully", "duration": 5.9359860000001845, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Sliding Window Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Sliding Window Rate Limiting should work with sliding window", "status": "passed", "title": "should work with sliding window", "duration": 0.8619719999999234, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Convenience Functions"], "fullName": "PostgreSQL Rate Limiting Convenience Functions should check API rate limit", "status": "passed", "title": "should check API rate limit", "duration": 0.4288500000000113, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Convenience Functions"], "fullName": "PostgreSQL Rate Limiting Convenience Functions should check auth rate limit", "status": "passed", "title": "should check auth rate limit", "duration": 1.5764079999999012, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should cleanup expired rate limits", "status": "passed", "title": "should cleanup expired rate limits", "duration": 1.9710030000001098, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should get rate limit stats", "status": "passed", "title": "should get rate limit stats", "duration": 0.5079139999998006, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should reset rate limit for user", "status": "passed", "title": "should reset rate limit for user", "duration": 1.104968000000099, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should handle empty timestamps array", "status": "passed", "title": "should handle empty timestamps array", "duration": 0.4227589999998145, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should handle malformed timestamps", "status": "passed", "title": "should handle malformed timestamps", "duration": 1.5040080000001126, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should filter out old timestamps", "status": "passed", "title": "should filter out old timestamps", "duration": 0.320555000000013, "failureMessages": [], "meta": {}}], "startTime": 1756113841771, "endTime": 1756113841788.504, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/postgresql-rate-limit.test.ts"}, {"assertionResults": [{"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should render without TDZ errors", "status": "passed", "title": "should render without TDZ errors", "duration": 51.93433899999991, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should handle successful authentication and fetch saved companies", "status": "passed", "title": "should handle successful authentication and fetch saved companies", "duration": 14.910498999999845, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should handle empty saved companies list", "status": "passed", "title": "should handle empty saved companies list", "duration": 16.111644999999953, "failureMessages": [], "meta": {}}], "startTime": 1756113842009, "endTime": 1756113842092.1116, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/saved-companies-page.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide full analytics insights for premium users", "status": "passed", "title": "should provide full analytics insights for premium users", "duration": 2.256321000000071, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide detailed benefit ranking analytics for premium users", "status": "passed", "title": "should provide detailed benefit ranking analytics for premium users", "duration": 0.5011060000000498, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide company comparison analytics for premium users", "status": "passed", "title": "should provide company comparison analytics for premium users", "duration": 0.36432400000001053, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should provide limited preview data for non-premium users", "status": "passed", "title": "should provide limited preview data for non-premium users", "duration": 0.41829199999995126, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should show upgrade prompt for detailed analytics", "status": "passed", "title": "should show upgrade prompt for detailed analytics", "duration": 0.32743600000003426, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should limit data export for non-premium users", "status": "passed", "title": "should limit data export for non-premium users", "duration": 0.2982359999999744, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track analytics page views", "status": "passed", "title": "should track analytics page views", "duration": 0.19768499999997857, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track insight interactions", "status": "passed", "title": "should track insight interactions", "duration": 0.14305500000000393, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track upgrade prompt interactions", "status": "passed", "title": "should track upgrade prompt interactions", "duration": 0.232845999999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Data Refresh"], "fullName": "User Analytics and Insights Flows Analytics Data Refresh should refresh user analytics data", "status": "passed", "title": "should refresh user analytics data", "duration": 0.5302490000000262, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Data Refresh"], "fullName": "User Analytics and Insights Flows Analytics Data Refresh should handle analytics refresh rate limiting", "status": "passed", "title": "should handle analytics refresh rate limiting", "duration": 0.17125499999997373, "failureMessages": [], "meta": {}}], "startTime": 1756113842361, "endTime": 1756113842367.1711, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-analytics-insights.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should handle user sign up with email", "status": "passed", "title": "should handle user sign up with email", "duration": 3.6897179999999707, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should handle duplicate email registration", "status": "passed", "title": "should handle duplicate email registration", "duration": 0.5623069999999188, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should validate required fields", "status": "passed", "title": "should validate required fields", "duration": 0.3426470000000563, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle user sign in with email", "status": "passed", "title": "should handle user sign in with email", "duration": 0.2505019999999831, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle non-existent email", "status": "passed", "title": "should handle non-existent email", "duration": 0.2074160000000802, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle rate limiting for sign in attempts", "status": "passed", "title": "should handle rate limiting for sign in attempts", "duration": 0.20093099999996866, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should verify valid magic link token", "status": "passed", "title": "should verify valid magic link token", "duration": 0.19285000000002128, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should handle expired magic link token", "status": "passed", "title": "should handle expired magic link token", "duration": 0.12799600000005285, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should handle invalid magic link token", "status": "passed", "title": "should handle invalid magic link token", "duration": 0.17038100000002032, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign Out Flow"], "fullName": "User Authentication Flows User Sign Out Flow should handle user sign out", "status": "passed", "title": "should handle user sign out", "duration": 0.19220899999993435, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Profile Update Flow"], "fullName": "User Authentication Flows Profile Update Flow should update user profile information", "status": "passed", "title": "should update user profile information", "duration": 0.1636230000000296, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Profile Update Flow"], "fullName": "User Authentication Flows Profile Update Flow should handle unauthorized profile update", "status": "passed", "title": "should handle unauthorized profile update", "duration": 0.1842069999999012, "failureMessages": [], "meta": {}}], "startTime": 1756113841616, "endTime": 1756113841622.1921, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-authentication.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should add benefit to personal ranking", "status": "passed", "title": "should add benefit to personal ranking", "duration": 1.664239000000066, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should remove benefit from personal ranking", "status": "passed", "title": "should remove benefit from personal ranking", "duration": 0.407224000000042, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should reorder benefits in personal ranking", "status": "passed", "title": "should reorder benefits in personal ranking", "duration": 1.0861629999999423, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should reset personal benefit ranking", "status": "passed", "title": "should reset personal benefit ranking", "duration": 0.23701500000004216, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should get user benefit ranking", "status": "passed", "title": "should get user benefit ranking", "duration": 0.3632509999999911, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Company Benefit Management"], "fullName": "User Benefit Management Flows Company Benefit Management should add benefit to company benefit list", "status": "passed", "title": "should add benefit to company benefit list", "duration": 0.4156800000000658, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Company Benefit Management"], "fullName": "User Benefit Management Flows Company Benefit Management should handle unauthorized benefit addition", "status": "passed", "title": "should handle unauthorized benefit addition", "duration": 0.32429000000001906, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should create benefit verification (confirm)", "status": "passed", "title": "should create benefit verification (confirm)", "duration": 0.3335070000000542, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should create benefit dispute", "status": "passed", "title": "should create benefit dispute", "duration": 0.5126849999999195, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should check authorization for benefit verification", "status": "passed", "title": "should check authorization for benefit verification", "duration": 0.371654000000035, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should handle unauthorized verification attempt", "status": "passed", "title": "should handle unauthorized verification attempt", "duration": 0.17287999999996373, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should get all available benefits", "status": "passed", "title": "should get all available benefits", "duration": 0.29528800000002775, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 0.20679299999994782, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should search benefits by name", "status": "passed", "title": "should search benefits by name", "duration": 0.4143060000000105, "failureMessages": [], "meta": {}}], "startTime": 1756113841655, "endTime": 1756113841661.4143, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-benefit-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should search companies by name", "status": "passed", "title": "should search companies by name", "duration": 1.7005509999999049, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should filter companies by location", "status": "passed", "title": "should filter companies by location", "duration": 0.28895299999999224, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should filter companies by benefits", "status": "passed", "title": "should filter companies by benefits", "duration": 0.457271999999989, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should handle empty search results", "status": "passed", "title": "should handle empty search results", "duration": 0.25133600000003753, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Profile Viewing"], "fullName": "User Company Interaction Flows Company Profile Viewing should get company profile details", "status": "passed", "title": "should get company profile details", "duration": 0.31142099999999573, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Profile Viewing"], "fullName": "User Company Interaction Flows Company Profile Viewing should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 0.17889600000000883, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should save a company to user favorites", "status": "passed", "title": "should save a company to user favorites", "duration": 0.5913059999999177, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should get user saved companies", "status": "passed", "title": "should get user saved companies", "duration": 0.643777, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should delete saved company", "status": "passed", "title": "should delete saved company", "duration": 0.3238529999999855, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should handle duplicate save attempt", "status": "passed", "title": "should handle duplicate save attempt", "duration": 0.33794399999999314, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should report missing company", "status": "passed", "title": "should report missing company", "duration": 0.266065000000026, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should validate required fields for missing company report", "status": "passed", "title": "should validate required fields for missing company report", "duration": 0.2595730000000458, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should get user submitted missing company reports", "status": "passed", "title": "should get user submitted missing company reports", "duration": 0.2836059999999634, "failureMessages": [], "meta": {}}], "startTime": 1756113842372, "endTime": 1756113842378.338, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-company-interactions.test.ts"}]}, "integration": {"numTotalTestSuites": 19, "numPassedTestSuites": 19, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 64, "numPassedTests": 64, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1756113852795, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify component modules can be imported", "status": "passed", "title": "should verify component modules can be imported", "duration": 184.56436399999984, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify admin component modules can be imported", "status": "passed", "title": "should verify admin component modules can be imported", "duration": 407.68162499999926, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify company dashboard components can be imported", "status": "passed", "title": "should verify company dashboard components can be imported", "duration": 180.59201099999882, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify batch benefit selection component can be imported", "status": "passed", "title": "should verify batch benefit selection component can be imported", "duration": 26.474233000000822, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify analytics components can be imported", "status": "passed", "title": "should verify analytics components can be imported", "duration": 65.52251199999955, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify benefit ranking component can be imported", "status": "passed", "title": "should verify benefit ranking component can be imported", "duration": 46.59947199999988, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate component props interface", "status": "passed", "title": "should validate component props interface", "duration": 20.221099000000322, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate benefit ranking props interface", "status": "passed", "title": "should validate benefit ranking props interface", "duration": 18.084359000000404, "failureMessages": [], "meta": {}}], "startTime": 1756113869842, "endTime": 1756113870792.0845, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/component-integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user profile retrieval", "status": "passed", "title": "should handle user profile retrieval", "duration": 228.811763, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user email change", "status": "passed", "title": "should handle user email change", "duration": 625.5997560000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should reject unauthorized profile access", "status": "passed", "title": "should reject unauthorized profile access", "duration": 51.59641899999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle invalid session tokens", "status": "passed", "title": "should handle invalid session tokens", "duration": 53.71066399999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return companies with pagination and filters", "status": "passed", "title": "should return companies with pagination and filters", "duration": 219.53988600000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by location", "status": "passed", "title": "should filter companies by location", "duration": 1402.7158790000003, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by benefits", "status": "passed", "title": "should filter companies by benefits", "duration": 123.04334400000016, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by industry", "status": "passed", "title": "should filter companies by industry", "duration": 48.67903100000012, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by size", "status": "passed", "title": "should filter companies by size", "duration": 49.240815999999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return detailed company information", "status": "passed", "title": "should return detailed company information", "duration": 50.862740999999914, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 39.20883200000026, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should search companies by name", "status": "passed", "title": "should search companies by name", "duration": 54.79184299999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should return benefits in correct format", "status": "passed", "title": "should return benefits in correct format", "duration": 537.5324860000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 52.471762000000126, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should search benefits by name", "status": "passed", "title": "should search benefits by name", "duration": 61.20112500000005, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow authenticated users to add benefits to their company", "status": "passed", "title": "should allow authenticated users to add benefits to their company", "duration": 792.9687110000004, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should reject unauthorized benefit additions", "status": "passed", "title": "should reject unauthorized benefit additions", "duration": 56.8094430000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow users to remove benefits from their company", "status": "passed", "title": "should allow users to remove benefits from their company", "duration": 671.325691, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to create benefit rankings", "status": "passed", "title": "should allow users to create benefit rankings", "duration": 244.17182499999944, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to update benefit rankings", "status": "passed", "title": "should allow users to update benefit rankings", "duration": 56.790052999999716, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to add benefits to existing rankings", "status": "passed", "title": "should allow users to add benefits to existing rankings", "duration": 658.9407510000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to remove benefits from rankings", "status": "passed", "title": "should allow users to remove benefits from rankings", "duration": 62.91805000000022, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to reset all benefit rankings", "status": "passed", "title": "should allow users to reset all benefit rankings", "duration": 53.95972499999971, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should retrieve user benefit rankings", "status": "passed", "title": "should retrieve user benefit rankings", "duration": 45.45513300000039, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to report missing companies", "status": "passed", "title": "should allow users to report missing companies", "duration": 432.99888100000044, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should prevent duplicate missing company reports", "status": "passed", "title": "should prevent duplicate missing company reports", "duration": 64.75130899999931, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to view their missing company reports", "status": "passed", "title": "should allow users to view their missing company reports", "duration": 412.3067369999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to save companies", "status": "passed", "title": "should allow users to save companies", "duration": 410.22676500000034, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to unsave companies", "status": "passed", "title": "should allow users to unsave companies", "duration": 638.9480499999991, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should retrieve user saved companies", "status": "passed", "title": "should retrieve user saved companies", "duration": 57.6294199999993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should prevent duplicate saved companies", "status": "passed", "title": "should prevent duplicate saved companies", "duration": 44.68170199999986, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should allow premium users to access analytics", "status": "passed", "title": "should allow premium users to access analytics", "duration": 251.9876720000011, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should restrict non-premium users to preview analytics", "status": "passed", "title": "should restrict non-premium users to preview analytics", "duration": 56.32145800000035, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should track analytics events", "status": "passed", "title": "should track analytics events", "duration": 446.16606499999943, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should handle anonymous analytics tracking", "status": "passed", "title": "should handle anonymous analytics tracking", "duration": 40.903824999999415, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should provide benefit ranking analytics for premium users", "status": "passed", "title": "should provide benefit ranking analytics for premium users", "duration": 269.25623500000074, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to submit benefit verifications", "status": "passed", "title": "should allow users to submit benefit verifications", "duration": 369.0758079999996, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should prevent duplicate benefit verifications", "status": "passed", "title": "should prevent duplicate benefit verifications", "duration": 76.97450199999912, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to view their benefit verification submissions", "status": "passed", "title": "should allow users to view their benefit verification submissions", "duration": 499.132368999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view pending benefit verifications", "status": "passed", "title": "should allow admins to view pending benefit verifications", "duration": 336.79310799999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve benefit verifications", "status": "passed", "title": "should allow admins to approve benefit verifications", "duration": 907.2303310000007, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reject benefit verifications", "status": "passed", "title": "should allow admins to reject benefit verifications", "duration": 110.5495289999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view missing company reports", "status": "passed", "title": "should allow admins to view missing company reports", "duration": 653.880102000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve missing company reports", "status": "passed", "title": "should allow admins to approve missing company reports", "duration": 820.4102590000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reset user analytics", "status": "passed", "title": "should allow admins to reset user analytics", "duration": 567.8616379999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should restrict admin endpoints to admin users only", "status": "passed", "title": "should restrict admin endpoints to admin users only", "duration": 55.41226599999936, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle malformed JSON requests", "status": "passed", "title": "should handle malformed JSON requests", "duration": 78.27923600000031, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle missing required fields", "status": "passed", "title": "should handle missing required fields", "duration": 44.38651600000048, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle database connection errors gracefully", "status": "passed", "title": "should handle database connection errors gracefully", "duration": 57.3583409999992, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 192.00914899999952, "failureMessages": [], "meta": {}}], "startTime": 1756113855641, "endTime": 1756113869785.009, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/comprehensive-app-functionality.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should connect to the database", "status": "passed", "title": "should connect to the database", "duration": 7.8012870000002295, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should verify required tables exist", "status": "passed", "title": "should verify required tables exist", "duration": 3.2033009999995556, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefit categories", "status": "passed", "title": "should be able to query benefit categories", "duration": 1.036098000000493, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefits", "status": "passed", "title": "should be able to query benefits", "duration": 0.7365090000002965, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query companies", "status": "passed", "title": "should be able to query companies", "duration": 0.8640980000000127, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to insert and delete test data", "status": "passed", "title": "should be able to insert and delete test data", "duration": 3.171464000000924, "failureMessages": [], "meta": {}}], "startTime": 1756113870811, "endTime": 1756113870828.1714, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/simple-database.test.ts"}]}, "e2e": null}}