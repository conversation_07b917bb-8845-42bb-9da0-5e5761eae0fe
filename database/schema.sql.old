-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Companies table (updated for multi-location support)
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL, -- Kept for backward compatibility, will be deprecated
    size VARCHAR(50) CHECK (size IN ('startup', 'small', 'medium', 'large', 'enterprise')) NOT NULL,
    industry VARCHAR(255) NOT NULL,
    description TEXT,
    domain VARCHAR(255),
    career_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company locations table for multi-location support
CREATE TABLE company_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    location_raw VARCHAR(255) NOT NULL, -- Original input location
    location_normalized VARCHAR(255) NOT NULL, -- Standardized location (e.g., "Munich, Germany")
    city VARCHAR(100), -- Extracted city name
    country VARCHAR(100), -- Extracted country name
    country_code VARCHAR(2), -- ISO country code (e.g., "DE", "US")
    latitude DECIMAL(10, 8), -- Geocoded latitude
    longitude DECIMAL(11, 8), -- Geocoded longitude
    is_primary BOOLEAN DEFAULT FALSE, -- Primary location for the company
    is_headquarters BOOLEAN DEFAULT FALSE, -- Headquarters location
    location_type VARCHAR(50) DEFAULT 'office' CHECK (location_type IN ('office', 'headquarters', 'branch', 'remote')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, location_normalized) -- Prevent duplicate normalized locations per company
);



-- Benefit categories table
CREATE TABLE benefit_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefits table
CREATE TABLE benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    category VARCHAR(50) CHECK (category IN ('health', 'time_off', 'financial', 'development', 'wellness', 'work_life', 'other')) NOT NULL,
    icon VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT,
    category_id UUID NOT NULL REFERENCES benefit_categories(id)
);

-- Company benefits junction table
CREATE TABLE company_benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    added_by VARCHAR(255), -- User ID from auth system
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, benefit_id)
);

-- Company users table (for company representatives)
CREATE TABLE company_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, email)
);

-- Benefit verifications table
CREATE TABLE benefit_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    status VARCHAR(50) CHECK (status IN ('confirmed', 'disputed')) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Saved companies table (for user bookmarks)
CREATE TABLE saved_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, company_id)
);

-- Users table for local authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    payment_status VARCHAR(50) DEFAULT 'free' CHECK (payment_status IN ('free', 'paying')),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL
);

-- Sessions table for local authentication
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Magic link tokens table
CREATE TABLE magic_link_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    user_data JSONB,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Magic link rate limits table
CREATE TABLE magic_link_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company verification tokens table
CREATE TABLE company_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User benefit rankings table
CREATE TABLE user_benefit_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10), -- 1 = most important, 10 = least important
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, benefit_id)
);

-- Missing company reports table
CREATE TABLE missing_company_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_email VARCHAR(255) NOT NULL,
    email_domain VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) CHECK (status IN ('pending', 'reviewed', 'added', 'rejected')) DEFAULT 'pending',
    admin_notes TEXT,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity log table
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT NOT NULL,
    user_id VARCHAR(255),
    user_email VARCHAR(255),
    user_name VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    company_name VARCHAR(255),
    benefit_id UUID REFERENCES benefits(id) ON DELETE SET NULL,
    benefit_name VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration log table
CREATE TABLE migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefit removal disputes table
CREATE TABLE benefit_removal_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL, -- Reason for requesting removal
    status VARCHAR(50) CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')) DEFAULT 'pending',
    admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Admin who approved/rejected
    admin_comment TEXT, -- Admin's comment on the decision
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure one dispute per user per benefit
    UNIQUE(company_benefit_id, user_id)
);

-- Company page views tracking
CREATE TABLE company_page_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous views
    session_id VARCHAR(255), -- For tracking anonymous sessions
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search queries tracking
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous searches
    session_id VARCHAR(255), -- For tracking anonymous sessions
    results_count INTEGER DEFAULT 0,
    filters_applied JSONB, -- Store applied filters as JSON
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefit search interactions (when users click on benefits in search results)
CREATE TABLE benefit_search_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_query_id UUID REFERENCES search_queries(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) CHECK (interaction_type IN ('view', 'click', 'verify', 'dispute')) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics summary tables for performance (daily aggregates)
CREATE TABLE daily_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    total_company_views INTEGER DEFAULT 0,
    total_searches INTEGER DEFAULT 0,
    total_benefit_interactions INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    unique_searchers INTEGER DEFAULT 0,
    top_searched_benefits JSONB, -- Array of {benefit_id, benefit_name, search_count}
    top_viewed_companies JSONB, -- Array of {company_id, company_name, view_count}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- Company analytics summary (for quick company-specific metrics)
CREATE TABLE company_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    benefit_interactions INTEGER DEFAULT 0,
    search_appearances INTEGER DEFAULT 0, -- How many times company appeared in search results
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, date)
);



-- Indexes for better performance
CREATE INDEX idx_companies_location ON companies(location);
CREATE INDEX idx_companies_size ON companies(size);
CREATE INDEX idx_companies_industry ON companies(industry);
CREATE INDEX idx_companies_domain ON companies(domain);

-- Indexes for company locations
CREATE INDEX idx_company_locations_company_id ON company_locations(company_id);
CREATE INDEX idx_company_locations_normalized ON company_locations(location_normalized);
CREATE INDEX idx_company_locations_city ON company_locations(city);
CREATE INDEX idx_company_locations_country ON company_locations(country);
CREATE INDEX idx_company_locations_country_code ON company_locations(country_code);
CREATE INDEX idx_company_locations_primary ON company_locations(is_primary) WHERE is_primary = true;
CREATE INDEX idx_company_locations_headquarters ON company_locations(is_headquarters) WHERE is_headquarters = true;
CREATE INDEX idx_company_locations_type ON company_locations(location_type);
CREATE INDEX idx_company_locations_coords ON company_locations(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;



CREATE INDEX idx_benefits_category ON benefits(category);
CREATE INDEX idx_benefits_category_id ON benefits(category_id);
CREATE INDEX idx_benefits_name ON benefits(name);

CREATE INDEX idx_benefit_categories_name ON benefit_categories(name);
CREATE INDEX idx_benefit_categories_sort_order ON benefit_categories(sort_order);
CREATE INDEX idx_benefit_categories_is_active ON benefit_categories(is_active);

CREATE INDEX idx_company_benefits_company_id ON company_benefits(company_id);
CREATE INDEX idx_company_benefits_benefit_id ON company_benefits(benefit_id);
CREATE INDEX idx_company_benefits_verified ON company_benefits(is_verified);

CREATE INDEX idx_company_users_company_id ON company_users(company_id);
CREATE INDEX idx_company_users_email ON company_users(email);

CREATE INDEX idx_benefit_verifications_company_benefit_id ON benefit_verifications(company_benefit_id);
CREATE INDEX idx_benefit_verifications_user_id ON benefit_verifications(user_id);
CREATE INDEX idx_benefit_verifications_status ON benefit_verifications(status);

CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);
CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);
CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);
CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_payment_status ON users(payment_status);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);

CREATE INDEX idx_magic_link_tokens_email ON magic_link_tokens(email);
CREATE INDEX idx_magic_link_tokens_token ON magic_link_tokens(token);
CREATE INDEX idx_magic_link_rate_limits_email ON magic_link_rate_limits(email);
CREATE INDEX idx_magic_link_rate_limits_window_start ON magic_link_rate_limits(window_start);

CREATE INDEX idx_company_verification_tokens_company_id ON company_verification_tokens(company_id);
CREATE INDEX idx_company_verification_tokens_user_id ON company_verification_tokens(user_id);
CREATE INDEX idx_company_verification_tokens_token ON company_verification_tokens(token);

CREATE INDEX idx_missing_company_reports_email_domain ON missing_company_reports(email_domain);
CREATE INDEX idx_missing_company_reports_status ON missing_company_reports(status);

CREATE INDEX idx_activity_log_event_type ON activity_log(event_type);
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_company_id ON activity_log(company_id);
CREATE INDEX idx_activity_log_created_at ON activity_log(created_at);

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON benefit_removal_disputes(company_benefit_id);
CREATE INDEX idx_benefit_removal_disputes_user_id ON benefit_removal_disputes(user_id);
CREATE INDEX idx_benefit_removal_disputes_status ON benefit_removal_disputes(status);
CREATE INDEX idx_benefit_removal_disputes_created_at ON benefit_removal_disputes(created_at);

-- Analytics table indexes
CREATE INDEX idx_company_page_views_company_id ON company_page_views(company_id);
CREATE INDEX idx_company_page_views_user_id ON company_page_views(user_id);
CREATE INDEX idx_company_page_views_session_id ON company_page_views(session_id);
CREATE INDEX idx_company_page_views_created_at ON company_page_views(created_at);

CREATE INDEX idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX idx_search_queries_session_id ON search_queries(session_id);
CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries USING gin(to_tsvector('english', query_text));

CREATE INDEX idx_benefit_search_interactions_search_query_id ON benefit_search_interactions(search_query_id);
CREATE INDEX idx_benefit_search_interactions_benefit_id ON benefit_search_interactions(benefit_id);
CREATE INDEX idx_benefit_search_interactions_company_id ON benefit_search_interactions(company_id);
CREATE INDEX idx_benefit_search_interactions_user_id ON benefit_search_interactions(user_id);
CREATE INDEX idx_benefit_search_interactions_created_at ON benefit_search_interactions(created_at);

CREATE INDEX idx_daily_analytics_summary_date ON daily_analytics_summary(date);
CREATE INDEX idx_company_analytics_summary_company_id ON company_analytics_summary(company_id);
CREATE INDEX idx_company_analytics_summary_date ON company_analytics_summary(date);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Clean up expired tokens function
CREATE OR REPLACE FUNCTION cleanup_expired_magic_links()
RETURNS void AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_categories_updated_at BEFORE UPDATE ON benefit_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON missing_company_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON daily_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON company_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system benefit categories
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_system) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1, true),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2, true),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3, true),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4, true),
('wellness', 'Wellness', 'Gym memberships, mental health support, wellness programs, and fitness benefits', '🧘', 5, true),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work options, and work-life balance benefits', '⚖️', 6, true),
('other', 'Other Benefits', 'Miscellaneous benefits that don\'t fit into other categories', '🎁', 7, true);

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_removal_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_benefit_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_page_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_search_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_locations ENABLE ROW LEVEL SECURITY;


-- Public read access for companies and benefits
CREATE POLICY "Public companies are viewable by everyone" ON companies
    FOR SELECT USING (true);

CREATE POLICY "Public benefits are viewable by everyone" ON benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company benefits are viewable by everyone" ON company_benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company locations are viewable by everyone" ON company_locations
    FOR SELECT USING (true);



-- Benefit removal disputes are viewable by admins and the users who created them
CREATE POLICY "Benefit removal disputes are viewable by admins" ON benefit_removal_disputes
    FOR SELECT USING (true); -- Will be restricted by application logic

-- User benefit rankings are only viewable and editable by the user who created them
CREATE POLICY "Users can view their own benefit rankings" ON user_benefit_rankings
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can insert their own benefit rankings" ON user_benefit_rankings
    FOR INSERT WITH CHECK (true); -- Will be restricted by application logic

CREATE POLICY "Users can update their own benefit rankings" ON user_benefit_rankings
    FOR UPDATE USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can delete their own benefit rankings" ON user_benefit_rankings
    FOR DELETE USING (true); -- Will be restricted by application logic

-- Analytics RLS Policies (Admin can see all, users can see their own data)
CREATE POLICY "Admin can view all company page views" ON company_page_views
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all search queries" ON search_queries
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all benefit interactions" ON benefit_search_interactions
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all analytics summaries" ON daily_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all company analytics" ON company_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic
