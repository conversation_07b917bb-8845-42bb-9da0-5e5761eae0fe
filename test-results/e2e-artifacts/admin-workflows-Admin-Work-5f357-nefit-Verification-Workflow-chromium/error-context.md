# Page snapshot

```yaml
- generic [active] [ref=e1]:
  - generic [ref=e2]:
    - banner [ref=e3]:
      - generic [ref=e4]:
        - link "✓ BenefitLens" [ref=e5] [cursor=pointer]:
          - /url: /
          - generic [ref=e7] [cursor=pointer]: ✓
          - generic [ref=e8] [cursor=pointer]: BenefitLens
        - navigation [ref=e9]:
          - link "Companies" [ref=e10] [cursor=pointer]:
            - /url: /
          - link "Benefits" [ref=e11] [cursor=pointer]:
            - /url: /benefits
          - link "About" [ref=e12] [cursor=pointer]:
            - /url: /about
        - generic [ref=e13]:
          - link "Sign In" [ref=e14] [cursor=pointer]:
            - /url: /sign-in
            - button "Sign In" [ref=e15]
          - link "Join your company" [ref=e16] [cursor=pointer]:
            - /url: /sign-up
            - button "Join your company" [ref=e17]:
              - generic [ref=e18]: Join your company
    - main [ref=e19]:
      - generic [ref=e21]:
        - generic [ref=e22]:
          - heading "404" [level=1] [ref=e23]
          - heading "Page Not Found" [level=2] [ref=e24]
          - paragraph [ref=e25]: The page you're looking for doesn't exist or has been moved.
        - generic [ref=e26]:
          - link "Go Home" [ref=e27] [cursor=pointer]:
            - /url: /
          - link "Browse Companies" [ref=e28] [cursor=pointer]:
            - /url: /companies
  - button "Open Next.js Dev Tools" [ref=e34] [cursor=pointer]:
    - img [ref=e35] [cursor=pointer]
  - alert [ref=e38]
```