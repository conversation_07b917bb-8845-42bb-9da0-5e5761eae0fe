{"numTotalTestSuites": 19, "numPassedTestSuites": 19, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 64, "numPassedTests": 64, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1756113852795, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify component modules can be imported", "status": "passed", "title": "should verify component modules can be imported", "duration": 184.56436399999984, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify admin component modules can be imported", "status": "passed", "title": "should verify admin component modules can be imported", "duration": 407.68162499999926, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify company dashboard components can be imported", "status": "passed", "title": "should verify company dashboard components can be imported", "duration": 180.59201099999882, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify batch benefit selection component can be imported", "status": "passed", "title": "should verify batch benefit selection component can be imported", "duration": 26.474233000000822, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify analytics components can be imported", "status": "passed", "title": "should verify analytics components can be imported", "duration": 65.52251199999955, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Module Integration"], "fullName": "Component Integration Tests Component Module Integration should verify benefit ranking component can be imported", "status": "passed", "title": "should verify benefit ranking component can be imported", "duration": 46.59947199999988, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate component props interface", "status": "passed", "title": "should validate component props interface", "duration": 20.221099000000322, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Component Integration Tests", "Component Props Validation"], "fullName": "Component Integration Tests Component Props Validation should validate benefit ranking props interface", "status": "passed", "title": "should validate benefit ranking props interface", "duration": 18.084359000000404, "failureMessages": [], "meta": {}}], "startTime": 1756113869842, "endTime": 1756113870792.0845, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/component-integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user profile retrieval", "status": "passed", "title": "should handle user profile retrieval", "duration": 228.811763, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle user email change", "status": "passed", "title": "should handle user email change", "duration": 625.5997560000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should reject unauthorized profile access", "status": "passed", "title": "should reject unauthorized profile access", "duration": 51.59641899999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Authentication & Profile Management"], "fullName": "Comprehensive App Functionality Integration Tests User Authentication & Profile Management should handle invalid session tokens", "status": "passed", "title": "should handle invalid session tokens", "duration": 53.71066399999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return companies with pagination and filters", "status": "passed", "title": "should return companies with pagination and filters", "duration": 219.53988600000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by location", "status": "passed", "title": "should filter companies by location", "duration": 1402.7158790000003, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by benefits", "status": "passed", "title": "should filter companies by benefits", "duration": 123.04334400000016, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by industry", "status": "passed", "title": "should filter companies by industry", "duration": 48.67903100000012, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should filter companies by size", "status": "passed", "title": "should filter companies by size", "duration": 49.240815999999995, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should return detailed company information", "status": "passed", "title": "should return detailed company information", "duration": 50.862740999999914, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 39.20883200000026, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Company Discovery & Management"], "fullName": "Comprehensive App Functionality Integration Tests Company Discovery & Management should search companies by name", "status": "passed", "title": "should search companies by name", "duration": 54.79184299999997, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should return benefits in correct format", "status": "passed", "title": "should return benefits in correct format", "duration": 537.5324860000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 52.471762000000126, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefits Management"], "fullName": "Comprehensive App Functionality Integration Tests Benefits Management should search benefits by name", "status": "passed", "title": "should search benefits by name", "duration": 61.20112500000005, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow authenticated users to add benefits to their company", "status": "passed", "title": "should allow authenticated users to add benefits to their company", "duration": 792.9687110000004, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should reject unauthorized benefit additions", "status": "passed", "title": "should reject unauthorized benefit additions", "duration": 56.8094430000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Management"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Management should allow users to remove benefits from their company", "status": "passed", "title": "should allow users to remove benefits from their company", "duration": 671.325691, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to create benefit rankings", "status": "passed", "title": "should allow users to create benefit rankings", "duration": 244.17182499999944, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to update benefit rankings", "status": "passed", "title": "should allow users to update benefit rankings", "duration": 56.790052999999716, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to add benefits to existing rankings", "status": "passed", "title": "should allow users to add benefits to existing rankings", "duration": 658.9407510000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to remove benefits from rankings", "status": "passed", "title": "should allow users to remove benefits from rankings", "duration": 62.91805000000022, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should allow users to reset all benefit rankings", "status": "passed", "title": "should allow users to reset all benefit rankings", "duration": 53.95972499999971, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "User Benefit Rankings"], "fullName": "Comprehensive App Functionality Integration Tests User Benefit Rankings should retrieve user benefit rankings", "status": "passed", "title": "should retrieve user benefit rankings", "duration": 45.45513300000039, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to report missing companies", "status": "passed", "title": "should allow users to report missing companies", "duration": 432.99888100000044, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should prevent duplicate missing company reports", "status": "passed", "title": "should prevent duplicate missing company reports", "duration": 64.75130899999931, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Missing Company Reports"], "fullName": "Comprehensive App Functionality Integration Tests Missing Company Reports should allow users to view their missing company reports", "status": "passed", "title": "should allow users to view their missing company reports", "duration": 412.3067369999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to save companies", "status": "passed", "title": "should allow users to save companies", "duration": 410.22676500000034, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should allow users to unsave companies", "status": "passed", "title": "should allow users to unsave companies", "duration": 638.9480499999991, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should retrieve user saved companies", "status": "passed", "title": "should retrieve user saved companies", "duration": 57.6294199999993, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Saved Companies"], "fullName": "Comprehensive App Functionality Integration Tests Saved Companies should prevent duplicate saved companies", "status": "passed", "title": "should prevent duplicate saved companies", "duration": 44.68170199999986, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should allow premium users to access analytics", "status": "passed", "title": "should allow premium users to access analytics", "duration": 251.9876720000011, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should restrict non-premium users to preview analytics", "status": "passed", "title": "should restrict non-premium users to preview analytics", "duration": 56.32145800000035, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should track analytics events", "status": "passed", "title": "should track analytics events", "duration": 446.16606499999943, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should handle anonymous analytics tracking", "status": "passed", "title": "should handle anonymous analytics tracking", "duration": 40.903824999999415, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Analytics & Insights"], "fullName": "Comprehensive App Functionality Integration Tests Analytics & Insights should provide benefit ranking analytics for premium users", "status": "passed", "title": "should provide benefit ranking analytics for premium users", "duration": 269.25623500000074, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to submit benefit verifications", "status": "passed", "title": "should allow users to submit benefit verifications", "duration": 369.0758079999996, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should prevent duplicate benefit verifications", "status": "passed", "title": "should prevent duplicate benefit verifications", "duration": 76.97450199999912, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Benefit Verification"], "fullName": "Comprehensive App Functionality Integration Tests Benefit Verification should allow users to view their benefit verification submissions", "status": "passed", "title": "should allow users to view their benefit verification submissions", "duration": 499.132368999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view pending benefit verifications", "status": "passed", "title": "should allow admins to view pending benefit verifications", "duration": 336.79310799999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve benefit verifications", "status": "passed", "title": "should allow admins to approve benefit verifications", "duration": 907.2303310000007, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reject benefit verifications", "status": "passed", "title": "should allow admins to reject benefit verifications", "duration": 110.5495289999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to view missing company reports", "status": "passed", "title": "should allow admins to view missing company reports", "duration": 653.880102000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to approve missing company reports", "status": "passed", "title": "should allow admins to approve missing company reports", "duration": 820.4102590000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should allow admins to reset user analytics", "status": "passed", "title": "should allow admins to reset user analytics", "duration": 567.8616379999985, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Admin Functionality"], "fullName": "Comprehensive App Functionality Integration Tests Admin Functionality should restrict admin endpoints to admin users only", "status": "passed", "title": "should restrict admin endpoints to admin users only", "duration": 55.41226599999936, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle malformed JSON requests", "status": "passed", "title": "should handle malformed JSON requests", "duration": 78.27923600000031, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle missing required fields", "status": "passed", "title": "should handle missing required fields", "duration": 44.38651600000048, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle database connection errors gracefully", "status": "passed", "title": "should handle database connection errors gracefully", "duration": 57.3583409999992, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Comprehensive App Functionality Integration Tests", "Error Handling & Edge Cases"], "fullName": "Comprehensive App Functionality Integration Tests Error Handling & Edge Cases should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 192.00914899999952, "failureMessages": [], "meta": {}}], "startTime": 1756113855641, "endTime": 1756113869785.009, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/comprehensive-app-functionality.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should connect to the database", "status": "passed", "title": "should connect to the database", "duration": 7.8012870000002295, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should verify required tables exist", "status": "passed", "title": "should verify required tables exist", "duration": 3.2033009999995556, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefit categories", "status": "passed", "title": "should be able to query benefit categories", "duration": 1.036098000000493, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query benefits", "status": "passed", "title": "should be able to query benefits", "duration": 0.7365090000002965, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to query companies", "status": "passed", "title": "should be able to query companies", "duration": 0.8640980000000127, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Database Integration Test"], "fullName": "Simple Database Integration Test should be able to insert and delete test data", "status": "passed", "title": "should be able to insert and delete test data", "duration": 3.171464000000924, "failureMessages": [], "meta": {}}], "startTime": 1756113870811, "endTime": 1756113870828.1714, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/integration/simple-database.test.ts"}]}