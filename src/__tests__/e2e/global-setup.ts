/**
 * Global setup for E2E tests
 * Prepares test database and environment
 */

import { chromium, FullConfig } from '@playwright/test'
import { query } from '@/lib/local-db'

async function globalSetup(config: FullConfig) {
  console.log('🎭 Starting E2E test setup...')

  try {
    // Skip database setup for now due to connection issues
    // TODO: Fix database connection for E2E tests
    console.log('🎭 Skipping database setup for E2E tests')

    // Wait for server to be ready
    await waitForServer(config.webServer?.url || 'http://localhost:3000')

    console.log('✅ E2E test setup complete')
  } catch (error) {
    console.warn('⚠️ E2E test setup had issues:', error)
    // For now, don't throw to allow tests to run even with database issues
    // In production, you might want to throw here to fail fast
  }
}

async function setupE2EDatabase() {
  try {
    // Clean up any existing E2E test data
    await cleanupE2EDatabase()
    
    // Create E2E test data
    await seedE2EDatabase()
    
    console.log('🎭 E2E test database setup complete')
  } catch (error) {
    console.error('❌ E2E test database setup failed:', error)
    throw error
  }
}

async function cleanupE2EDatabase() {
  const cleanupQueries = [
    'DELETE FROM user_benefit_rankings WHERE user_id LIKE \'e2e-%\'',
    'DELETE FROM benefit_removal_disputes WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM benefit_verifications WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM company_locations WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM saved_companies WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM users WHERE email LIKE \'%@e2e.test\'',
    'DELETE FROM companies WHERE domain LIKE \'%.e2e\'',
    'DELETE FROM benefits WHERE name LIKE \'E2E %\'',
  ]

  for (const sql of cleanupQueries) {
    try {
      await query(sql)
    } catch (error) {
      console.warn('Cleanup query failed (may be expected):', sql, error)
    }
  }
}

async function seedE2EDatabase() {
  // Create E2E test benefit categories
  await query(`
    INSERT INTO benefit_categories (id, name, display_name, icon) 
    VALUES 
      ('e2e-cat-1', 'e2e_health', 'E2E Health', '🏥'),
      ('e2e-cat-2', 'e2e_flexibility', 'E2E Flexibility', '⏰'),
      ('e2e-cat-3', 'e2e_financial', 'E2E Financial', '💰')
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test benefits
  await query(`
    INSERT INTO benefits (id, name, description, category_id, icon) 
    VALUES 
      ('e2e-benefit-1', 'E2E Health Insurance', 'Comprehensive health coverage for E2E testing', 'e2e-cat-1', '🏥'),
      ('e2e-benefit-2', 'E2E Remote Work', 'Flexible remote work policy for E2E testing', 'e2e-cat-2', '🏠'),
      ('e2e-benefit-3', 'E2E Dental Coverage', 'Dental insurance for E2E testing', 'e2e-cat-1', '🦷'),
      ('e2e-benefit-4', 'E2E Stock Options', 'Employee stock options for E2E testing', 'e2e-cat-3', '📈'),
      ('e2e-benefit-5', 'E2E Gym Membership', 'Fitness center membership for E2E testing', 'e2e-cat-1', '💪')
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test companies
  await query(`
    INSERT INTO companies (id, name, domain, industry, size, verification_status, description) 
    VALUES 
      ('e2e-company-1', 'E2E Tech Corp', 'techcorp.e2e', 'Technology', '100-500', 'verified', 'Leading technology company for E2E testing'),
      ('e2e-company-2', 'E2E Industries', 'industries.e2e', 'Manufacturing', '500-1000', 'verified', 'Manufacturing company for E2E testing'),
      ('e2e-company-3', 'E2E Startup', 'startup.e2e', 'Technology', '10-50', 'pending', 'Innovative startup for E2E testing'),
      ('e2e-company-4', 'E2E Consulting', 'consulting.e2e', 'Consulting', '50-100', 'unverified', 'Consulting firm for E2E testing')
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test users
  await query(`
    INSERT INTO users (id, email, first_name, last_name, role, company_domain, is_premium, created_at) 
    VALUES 
      ('e2e-user-1', 'user1@techcorp.e2e', 'John', 'Doe', 'user', 'techcorp.e2e', false, NOW()),
      ('e2e-user-2', 'user2@industries.e2e', 'Jane', 'Smith', 'user', 'industries.e2e', true, NOW()),
      ('e2e-user-3', 'user3@startup.e2e', 'Bob', 'Johnson', 'user', 'startup.e2e', false, NOW()),
      ('e2e-admin-1', '<EMAIL>', 'Admin', 'User', 'admin', null, true, NOW()),
      ('e2e-super-admin', '<EMAIL>', 'Super', 'Admin', 'super_admin', null, true, NOW())
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test company benefits
  await query(`
    INSERT INTO company_benefits (id, company_id, benefit_id, is_verified, verified_by, verified_at) 
    VALUES 
      ('e2e-cb-1', 'e2e-company-1', 'e2e-benefit-1', true, 'e2e-admin-1', NOW()),
      ('e2e-cb-2', 'e2e-company-1', 'e2e-benefit-2', true, 'e2e-admin-1', NOW()),
      ('e2e-cb-3', 'e2e-company-1', 'e2e-benefit-4', true, 'e2e-admin-1', NOW()),
      ('e2e-cb-4', 'e2e-company-2', 'e2e-benefit-1', true, 'e2e-admin-1', NOW()),
      ('e2e-cb-5', 'e2e-company-2', 'e2e-benefit-3', false, null, null),
      ('e2e-cb-6', 'e2e-company-3', 'e2e-benefit-2', false, null, null)
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test company locations
  await query(`
    INSERT INTO company_locations (id, company_id, city, country, country_code, is_headquarters, is_primary) 
    VALUES 
      ('e2e-loc-1', 'e2e-company-1', 'Berlin', 'Germany', 'DE', true, true),
      ('e2e-loc-2', 'e2e-company-1', 'Munich', 'Germany', 'DE', false, false),
      ('e2e-loc-3', 'e2e-company-2', 'Hamburg', 'Germany', 'DE', true, true),
      ('e2e-loc-4', 'e2e-company-3', 'Frankfurt', 'Germany', 'DE', true, true)
    ON CONFLICT (id) DO NOTHING
  `)

  // Create E2E test user benefit rankings
  await query(`
    INSERT INTO user_benefit_rankings (id, user_id, benefit_id, ranking, created_at) 
    VALUES 
      ('e2e-ubr-1', 'e2e-user-1', 'e2e-benefit-1', 1, NOW()),
      ('e2e-ubr-2', 'e2e-user-1', 'e2e-benefit-2', 2, NOW()),
      ('e2e-ubr-3', 'e2e-user-2', 'e2e-benefit-1', 1, NOW()),
      ('e2e-ubr-4', 'e2e-user-2', 'e2e-benefit-3', 2, NOW())
    ON CONFLICT (id) DO NOTHING
  `)

  console.log('🎭 E2E test database seeded')
}

async function waitForServer(url: string, maxAttempts = 60) {
  console.log(`🎭 Waiting for server at ${url}...`)
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`${url}/api/health`)
      if (response.ok) {
        console.log('✅ Server is ready')
        return true
      }
    } catch (error) {
      // Server not ready yet
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
  
  throw new Error(`Server not ready after ${maxAttempts * 2} seconds`)
}

export default globalSetup
