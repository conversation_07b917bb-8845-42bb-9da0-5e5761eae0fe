/**
 * Global teardown for E2E tests
 * Cleans up test database and environment
 */

import { query } from '@/lib/local-db'

async function globalTeardown() {
  console.log('🎭 Starting E2E test teardown...')
  
  try {
    // Skip database cleanup for now due to connection issues
    // TODO: Fix database connection for E2E tests
    console.log('🎭 Skipping database cleanup for E2E tests')

    console.log('✅ E2E test teardown complete')
  } catch (error) {
    console.error('❌ E2E test teardown failed:', error)
    // Don't throw - teardown failures shouldn't break the build
  }
}

async function cleanupE2EDatabase() {
  const cleanupQueries = [
    'DELETE FROM user_benefit_rankings WHERE user_id LIKE \'e2e-%\'',
    'DELETE FROM benefit_removal_disputes WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM benefit_verifications WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM company_locations WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM saved_companies WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\')',
    'DELETE FROM users WHERE email LIKE \'%@e2e.test\'',
    'DELETE FROM companies WHERE domain LIKE \'%.e2e\'',
    'DELETE FROM benefits WHERE name LIKE \'E2E %\'',
    'DELETE FROM benefit_categories WHERE name LIKE \'e2e_%\'',
  ]

  for (const sql of cleanupQueries) {
    try {
      await query(sql)
    } catch (error) {
      console.warn('Cleanup query failed (may be expected):', sql, error)
    }
  }
  
  console.log('🎭 E2E test database cleaned')
}

export default globalTeardown
