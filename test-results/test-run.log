Test run started at Mo 25. Aug 11:23:31 CEST 2025
[0;32m🚀 Starting comprehensive test suite for BenefitLens[0m
[0;34mChecking prerequisites...[0m
[1;33m⚠️  Database health check failed - some tests may fail[0m
[0;32m✅ Prerequisites check completed[0m
[0;34mSetting up test environment...[0m
[0;34mRunning: Building application[0m
Command: npm run build

> benefitlens@0.1.0 build
> next build

   ▲ Next.js 15.5.0
   - Environments: .env.local

   Creating an optimized production build ...
 ✓ Compiled successfully in 3.7s
   Skipping validation of types
   Skipping linting
   Collecting page data ...
{"timestamp":"2025-08-25T09:23:37.826Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:37.829Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:37.829Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:37.829Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:37.829Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:37.881Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:37.882Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:37.882Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:37.883Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:37.883Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.331Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.331Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.332Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.332Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.333Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.334Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.335Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.336Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.337Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.338Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.341Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.350Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.352Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.353Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.353Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.353Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.352Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.353Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.355Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.355Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.355Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.355Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.354Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.353Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.356Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.357Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.370Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.371Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.374Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.374Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.377Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.377Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.380Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.381Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.382Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.382Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.383Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:38.383Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.383Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.383Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:38.383Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.384Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:38.384Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.384Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:38.384Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
{"timestamp":"2025-08-25T09:23:38.384Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.385Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.385Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.386Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.386Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.389Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":53,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.389Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":44,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.390Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.390Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.391Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":53,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.391Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.391Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":50,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.392Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":2,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":81,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.546807,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":3,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":80,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":2.527641,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.393Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":56,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":3,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":81,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":3.403108,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.394Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":58,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":2,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":84,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":4,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.586966,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":90,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.821804,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.396Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.397Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.397Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.397Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":508,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.398Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":2,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.399Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.399Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.400Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":43,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.400Z","level":"debug","message":"Database Query","context":{"query":"SELECT delete_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.401Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.401Z","level":"debug","message":"Database Query","context":{"query":"SELECT 1 as test","duration":0,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.401Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":44,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":81,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.401Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.788689,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":3,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":19,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":4.11006,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.402Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":2,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":83,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":2.06544,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":84,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.450448,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.404Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.407Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.407Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.409Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.409Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.414Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":54,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.414Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":27,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.415Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.415Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.416Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":2,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"warn","message":"Cache health check failed"}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":84,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":2.056636,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.417Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.418Z","level":"debug","message":"Database Query","context":{"query":"SELECT delete_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.418Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:38.419Z","level":"debug","message":"Database Query","context":{"query":"SELECT 1 as test","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:38.419Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":82,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:38.419Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":1.826168,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:38.419Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:38.419Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
   Generating static pages (0/74) ...
   Generating static pages (18/74) 
{"timestamp":"2025-08-25T09:23:40.451Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:40.452Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:40.453Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.453Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.453Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
   Generating static pages (36/74) 
{"timestamp":"2025-08-25T09:23:40.546Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:40.547Z","level":"info","message":"Environment validation passed","context":{"warningCount":3,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:40.548Z","level":"warn","message":"Environment warning","context":{"warning":"Using default value for ENABLE_PERFORMANCE_MONITORING"}}
{"timestamp":"2025-08-25T09:23:40.548Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.548Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.563Z","level":"info","message":"Starting environment validation","context":{"buildPhase":false,"nodeEnv":"production"}}
{"timestamp":"2025-08-25T09:23:40.564Z","level":"info","message":"Environment validation passed","context":{"warningCount":2,"configuredVars":13}}
{"timestamp":"2025-08-25T09:23:40.564Z","level":"warn","message":"Environment warning","context":{"warning":"NEXT_PUBLIC_APP_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.564Z","level":"warn","message":"Environment warning","context":{"warning":"DATABASE_URL should not use localhost in production"}}
{"timestamp":"2025-08-25T09:23:40.564Z","level":"info","message":"Starting performance monitoring","context":{"intervalMs":300000}}
   Generating static pages (55/74) 
{"timestamp":"2025-08-25T09:23:40.648Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.649Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.661Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":130,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.661Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.662Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.663Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"debug","message":"Database Query","context":{"query":"SELECT delete_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"debug","message":"Database Query","context":{"query":"SELECT 1 as test","duration":0,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":85,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":77.000206,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:40.664Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
 ✓ Generating static pages (74/74)
{"timestamp":"2025-08-25T09:23:40.694Z","level":"debug","message":"Database connection established","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.694Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.698Z","level":"debug","message":"Database Query","context":{"query":"SELECT set_cache($1, $2, $3)","duration":78,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.698Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.699Z","level":"debug","message":"Database Query","context":{"query":"SELECT get_cache($1)","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.700Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.700Z","level":"debug","message":"Database Query","context":{"query":"SELECT delete_cache($1)","duration":0,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.700Z","level":"debug","message":"Database connection acquired from pool","context":{"totalCount":1,"idleCount":0,"waitingCount":0}}
{"timestamp":"2025-08-25T09:23:40.701Z","level":"debug","message":"Database Query","context":{"query":"SELECT 1 as test","duration":1,"rowCount":1}}
{"timestamp":"2025-08-25T09:23:40.701Z","level":"info","message":"Performance Metric","context":{"metric":"memory_heap_used","value":86,"unit":"MB"}}
{"timestamp":"2025-08-25T09:23:40.701Z","level":"info","message":"Performance Metric","context":{"metric":"event_loop_delay","value":55.340802,"unit":"ms"}}
{"timestamp":"2025-08-25T09:23:40.701Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_total","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:40.701Z","level":"info","message":"Performance Metric","context":{"metric":"database_connections_waiting","value":0,"unit":"count"}}
{"timestamp":"2025-08-25T09:23:40.764Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:40.764Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:40.765Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:40.764Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:40.764Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:40.765Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:40.764Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
   Finalizing page optimization ...
   Collecting build traces ...
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Database pool closed successfully"}
{"timestamp":"2025-08-25T09:23:41.454Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.455Z","level":"info","message":"Received SIGINT, shutting down gracefully"}
{"timestamp":"2025-08-25T09:23:41.456Z","level":"info","message":"Stopping performance monitoring"}
{"timestamp":"2025-08-25T09:23:41.456Z","level":"info","message":"Database pool closed successfully"}

Route (app)                                                         Size  First Load JS
┌ ○ /                                                            5.79 kB         123 kB
├ ○ /_not-found                                                    334 B         102 kB
├ ○ /about                                                         527 B         118 kB
├ ƒ /admin                                                       23.6 kB         141 kB
├ ƒ /analytics                                                   5.76 kB         123 kB
├ ƒ /api/admin/activities                                          334 B         102 kB
├ ƒ /api/admin/activities/all                                      334 B         102 kB
├ ƒ /api/admin/analytics/reset                                     334 B         102 kB
├ ƒ /api/admin/benefit-categories                                  334 B         102 kB
├ ƒ /api/admin/benefit-categories/[categoryId]                     334 B         102 kB
├ ƒ /api/admin/benefit-categories/[categoryId]/migrate             334 B         102 kB
├ ƒ /api/admin/benefit-rankings                                    334 B         102 kB
├ ƒ /api/admin/benefit-removal-disputes                            334 B         102 kB
├ ƒ /api/admin/benefit-verifications                               334 B         102 kB
├ ƒ /api/admin/benefit-verifications/[verificationId]              334 B         102 kB
├ ƒ /api/admin/benefits                                            334 B         102 kB
├ ƒ /api/admin/companies                                           334 B         102 kB
├ ƒ /api/admin/companies/[companyId]/benefits                      334 B         102 kB
├ ƒ /api/admin/companies/[companyId]/benefits/verify               334 B         102 kB
├ ƒ /api/admin/companies/[companyId]/discover-users                334 B         102 kB
├ ƒ /api/admin/companies/[companyId]/locations                     334 B         102 kB
├ ƒ /api/admin/company-benefits                                    334 B         102 kB
├ ƒ /api/admin/import/german-companies                             334 B         102 kB
├ ƒ /api/admin/missing-companies                                   334 B         102 kB
├ ƒ /api/admin/missing-companies/[reportId]                        334 B         102 kB
├ ƒ /api/admin/missing-company-reports                             334 B         102 kB
├ ƒ /api/admin/notify-missing-company-reporters                    334 B         102 kB
├ ƒ /api/admin/users                                               334 B         102 kB
├ ƒ /api/admin/users/[userId]                                      334 B         102 kB
├ ƒ /api/admin/users/[userId]/payment-status                       334 B         102 kB
├ ƒ /api/analytics/benefit-rankings                                334 B         102 kB
├ ƒ /api/analytics/benefit-trends                                  334 B         102 kB
├ ƒ /api/analytics/company-insights                                334 B         102 kB
├ ƒ /api/analytics/company/[id]                                    334 B         102 kB
├ ƒ /api/analytics/export                                          334 B         102 kB
├ ƒ /api/analytics/overview                                        334 B         102 kB
├ ƒ /api/analytics/search-trends                                   334 B         102 kB
├ ƒ /api/analytics/top-companies                                   334 B         102 kB
├ ƒ /api/analytics/track                                           334 B         102 kB
├ ƒ /api/analytics/user-insights                                   334 B         102 kB
├ ƒ /api/auth/csrf                                                 334 B         102 kB
├ ƒ /api/auth/magic-link                                           334 B         102 kB
├ ƒ /api/auth/me                                                   334 B         102 kB
├ ƒ /api/auth/sign-in                                              334 B         102 kB
├ ƒ /api/auth/sign-out                                             334 B         102 kB
├ ƒ /api/auth/sign-up                                              334 B         102 kB
├ ƒ /api/auth/update-profile                                       334 B         102 kB
├ ƒ /api/auth/validate-session                                     334 B         102 kB
├ ƒ /api/auth/verify-company                                       334 B         102 kB
├ ƒ /api/benefit-categories                                        334 B         102 kB
├ ƒ /api/benefit-removal-disputes                                  334 B         102 kB
├ ƒ /api/benefit-removal-disputes/[companyBenefitId]               334 B         102 kB
├ ƒ /api/benefit-removal-disputes/cancel                           334 B         102 kB
├ ƒ /api/benefit-removal-disputes/status                           334 B         102 kB
├ ƒ /api/benefit-verifications                                     334 B         102 kB
├ ƒ /api/benefit-verifications/[companyBenefitId]                  334 B         102 kB
├ ƒ /api/benefit-verifications/[companyBenefitId]/authorization    334 B         102 kB
├ ƒ /api/benefits                                                  334 B         102 kB
├ ƒ /api/companies                                                 334 B         102 kB
├ ƒ /api/companies/[id]                                            334 B         102 kB
├ ƒ /api/companies/[id]/authorization                              334 B         102 kB
├ ƒ /api/companies/[id]/benefits                                   334 B         102 kB
├ ƒ /api/companies/[id]/benefits/[benefitId]                       334 B         102 kB
├ ƒ /api/companies/[id]/benefits/bulk                              334 B         102 kB
├ ƒ /api/companies/[id]/benefits/export                            334 B         102 kB
├ ƒ /api/companies/[id]/benefits/import                            334 B         102 kB
├ ƒ /api/companies/[id]/locations                                  334 B         102 kB
├ ƒ /api/filter-options/benefits                                   334 B         102 kB
├ ƒ /api/filter-options/industries                                 334 B         102 kB
├ ƒ /api/health                                                    334 B         102 kB
├ ƒ /api/health/live                                               334 B         102 kB
├ ƒ /api/health/ready                                              334 B         102 kB
├ ƒ /api/health/simple                                             334 B         102 kB
├ ƒ /api/locations/suggestions                                     334 B         102 kB
├ ƒ /api/missing-companies                                         334 B         102 kB
├ ƒ /api/report-missing-company                                    334 B         102 kB
├ ƒ /api/saved-companies                                           334 B         102 kB
├ ƒ /api/saved-companies/[companyId]                               334 B         102 kB
├ ƒ /api/search                                                    334 B         102 kB
├ ƒ /api/user/benefit-rankings                                     334 B         102 kB
├ ƒ /api/user/benefit-rankings/[benefitId]                         334 B         102 kB
├ ƒ /api/user/benefit-verifications                                334 B         102 kB
├ ƒ /api/user/missing-company-reports                              334 B         102 kB
├ ƒ /api/user/profile                                              334 B         102 kB
├ ƒ /api/user/saved-companies                                      334 B         102 kB
├ ƒ /api/user/saved-companies/[companyId]                          334 B         102 kB
├ ○ /auth/magic-link                                             1.84 kB         108 kB
├ ○ /auth/verify-company                                         3.08 kB         113 kB
├ ○ /benefits                                                    2.12 kB         119 kB
├ ƒ /companies/[id]                                              4.92 kB         122 kB
├ ƒ /dashboard                                                   9.35 kB         127 kB
├ ○ /pricing                                                       527 B         118 kB
├ ƒ /rankings                                                    5.93 kB         123 kB
├ ○ /saved-companies                                             3.51 kB         121 kB
├ ○ /sign-in                                                      1.7 kB         119 kB
├ ○ /sign-up                                                     1.92 kB         119 kB
└ ○ /verify-company                                              1.53 kB         107 kB
+ First Load JS shared by all                                     102 kB
  ├ chunks/1255-20045df290cc858c.js                              45.7 kB
  ├ chunks/4bd1b696-f785427dddbba9fb.js                          54.2 kB
  └ other shared chunks (total)                                  2.01 kB


ƒ Middleware                                                     34.5 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

[0;32m✅ Building application completed successfully[0m
[0;32m✅ Test environment setup completed[0m
[0;34mRunning Unit Tests...[0m
[0;34mRunning: Unit tests[0m
Command: npm run test:unit -- --run --coverage --reporter=json --outputFile=test-results/unit-results.json

> benefitlens@0.1.0 test:unit
> vitest --config vitest.config.ts --run --coverage --reporter=json --outputFile=test-results/unit-results.json

JSON report written to /home/<USER>/git/workwell/test-results/unit-results.json
[34m % [39m[2mCoverage report from [22m[33mv8[39m
-------------------|---------|----------|---------|---------|-------------------
File               | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-------------------|---------|----------|---------|---------|-------------------
All files          |   14.87 |    29.32 |   22.22 |   14.87 |                   
 workwell          |       0 |        0 |       0 |       0 |                   
  ...nt-errors.cjs |       0 |        0 |       0 |       0 | 1-140             
  ...variables.cjs |       0 |        0 |       0 |       0 | 1-78              
 ...l/.next/server |       0 |        0 |       0 |       0 |                   
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
  ...d-manifest.js |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
  ...t-manifest.js |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
  ...ck-runtime.js |       0 |        0 |       0 |       0 | 1                 
 ...ext/server/app |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...app/_not-found |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...rver/app/about |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...rver/app/admin |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-19              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../app/analytics |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-12              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...min/activities |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-37              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...activities/all |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-37              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nalytics/reset |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-27              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-21              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...s/[categoryId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-22              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...oryId]/migrate |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-13              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-71              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-98              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-38              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...erificationId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-29              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...admin/benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-32              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...dmin/companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-77              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nyId]/benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-41              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/verify |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-26              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...discover-users |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...yId]/locations |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...mpany-benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-53              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...rman-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-20              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-27              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ies/[reportId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-20              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-40              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...pany-reporters |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-92              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...pi/admin/users |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-33              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...users/[userId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-48              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...payment-status |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-91              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...benefit-trends |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-49              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...mpany-insights |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-67              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...s/company/[id] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-29              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...alytics/export |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ytics/overview |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-101             
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../search-trends |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-91              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../top-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-91              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nalytics/track |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-101             
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../user-insights |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-31              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../api/auth/csrf |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...uth/magic-link |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...pp/api/auth/me |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...i/auth/sign-in |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-82              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../auth/sign-out |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...i/auth/sign-up |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-82              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...update-profile |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-14              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...lidate-session |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-8               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-12              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-67              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-44              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...isputes/cancel |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-56              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...isputes/status |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-44              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-50              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-14              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...p/api/benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-21              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../api/companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-37              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../[id]/benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ts/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../benefits/bulk |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/export |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/import |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...[id]/locations |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...tions/benefits |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-12              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ons/industries |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-9               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...app/api/health |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-7               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...pi/health/live |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...i/health/ready |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-6               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../health/simple |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ns/suggestions |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-9               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-31              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...issing-company |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-140             
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-34              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...app/api/search |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-43              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...gs/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-28              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-26              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-24              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...i/user/profile |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-14              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-46              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1-11              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...uth/magic-link |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...r/app/benefits |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../app/dashboard |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-12              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...pp/favicon.ico |       0 |        0 |       0 |       0 |                   
  route.js         |       0 |        0 |       0 |       0 | 1                 
 ...er/app/pricing |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...r/app/rankings |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-12              
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...er/app/sign-in |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...er/app/sign-up |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  page.js          |       0 |        0 |       0 |       0 | 1-2               
  ...e-manifest.js |       0 |        0 |       0 |       0 | 1                 
 .../server/chunks |       0 |        0 |       0 |       0 |                   
  1692.js          |       0 |        0 |       0 |       0 | 1                 
  2161.js          |       0 |        0 |       0 |       0 | 1                 
  2355.js          |       0 |        0 |       0 |       0 | 1-178             
  3334.js          |       0 |        0 |       0 |       0 | 1-11              
  3457.js          |       0 |        0 |       0 |       0 | 1                 
  3487.js          |       0 |        0 |       0 |       0 | 1-228             
  3552.js          |       0 |        0 |       0 |       0 | 1                 
  4229.js          |       0 |        0 |       0 |       0 | 1-169             
  4284.js          |       0 |        0 |       0 |       0 | 1                 
  4501.js          |       0 |        0 |       0 |       0 | 1-9               
  4586.js          |       0 |        0 |       0 |       0 | 1-22              
  5112.js          |       0 |        0 |       0 |       0 | 1                 
  5611.js          |       0 |        0 |       0 |       0 | 1-6               
  5965.js          |       0 |        0 |       0 |       0 | 1-189             
  6153.js          |       0 |        0 |       0 |       0 | 1                 
  6802.js          |       0 |        0 |       0 |       0 | 1                 
  7150.js          |       0 |        0 |       0 |       0 | 1                 
  9891.js          |       0 |        0 |       0 |       0 | 1                 
 ...t/server/pages |       0 |        0 |       0 |       0 |                   
  _app.js          |       0 |        0 |       0 |       0 | 1                 
  _document.js     |       0 |        0 |       0 |       0 | 1                 
  _error.js        |       0 |        0 |       0 |       0 | 1-19              
 ...ext/server/src |     100 |      100 |     100 |     100 |                   
  _N_E             |     100 |      100 |     100 |     100 |                   
 ...erver/src/_N_E |     100 |      100 |     100 |     100 |                   
  ...sync_hooks%22 |     100 |      100 |     100 |     100 |                   
  ...ode:buffer%22 |     100 |      100 |     100 |     100 |                   
 ...untime/cookies |     100 |      100 |     100 |     100 |                   
  index.js         |     100 |      100 |     100 |     100 |                   
 ...ntelemetry/api |     100 |      100 |     100 |     100 |                   
  index.js         |     100 |      100 |     100 |     100 |                   
 ...ompiled/cookie |     100 |      100 |     100 |     100 |                   
  index.js         |     100 |      100 |     100 |     100 |                   
 ...mpiled/p-queue |     100 |      100 |     100 |     100 |                   
  index.js         |     100 |      100 |     100 |     100 |                   
 ...compiled/react |     100 |      100 |     100 |     100 |                   
  ...act-server.js |     100 |      100 |     100 |     100 |                   
 ...iled/react/cjs |     100 |      100 |     100 |     100 |                   
  ...production.js |     100 |      100 |     100 |     100 |                   
 ...d/ua-parser-js |     100 |      100 |     100 |     100 |                   
  ua-parser.js     |     100 |      100 |     100 |     100 |                   
 ...m/build/output |     100 |      100 |     100 |     100 |                   
  log.js           |     100 |      100 |     100 |     100 |                   
 ...t/dist/esm/lib |     100 |      100 |     100 |     100 |                   
  constants.js     |     100 |      100 |     100 |     100 |                   
  picocolors.js    |     100 |      100 |     100 |     100 |                   
 ...ist/esm/server |     100 |      100 |     100 |     100 |                   
  ...ring-utils.js |     100 |      100 |     100 |     100 |                   
  ...rnal-utils.js |     100 |      100 |     100 |     100 |                   
  ...tion-utils.js |     100 |      100 |     100 |     100 |                   
 ...m/server/after |     100 |      100 |     100 |     100 |                   
  after-context.js |     100 |      100 |     100 |     100 |                   
  ...st-context.js |     100 |      100 |     100 |     100 |                   
 ...rver/api-utils |     100 |      100 |     100 |     100 |                   
  index.js         |     100 |      100 |     100 |     100 |                   
 ...ver/app-render |     100 |      100 |     100 |     100 |                   
  ...e-instance.js |     100 |      100 |     100 |     100 |                   
  ...e-instance.js |     100 |      100 |     100 |     100 |                   
  ...al-storage.js |     100 |      100 |     100 |     100 |                   
  ...orage.js?788b |     100 |      100 |     100 |     100 |                   
  ...e-instance.js |     100 |      100 |     100 |     100 |                   
  ...e-instance.js |     100 |      100 |     100 |     100 |                   
 .../async-storage |     100 |      100 |     100 |     100 |                   
  ...e-provider.js |     100 |      100 |     100 |     100 |                   
  request-store.js |     100 |      100 |     100 |     100 |                   
  work-store.js    |     100 |      100 |     100 |     100 |                   
 ...esm/server/lib |     100 |      100 |     100 |     100 |                   
  implicit-tags.js |     100 |      100 |     100 |     100 |                   
  lazy-result.js   |     100 |      100 |     100 |     100 |                   
  lru-cache.js     |     100 |      100 |     100 |     100 |                   
 ...rver/lib/trace |     100 |      100 |     100 |     100 |                   
  constants.js     |     100 |      100 |     100 |     100 |                   
  tracer.js        |     100 |      100 |     100 |     100 |                   
 ...rver/use-cache |     100 |      100 |     100 |     100 |                   
  handlers.js      |     100 |      100 |     100 |     100 |                   
 ...esm/server/web |     100 |      100 |     100 |     100 |                   
  adapter.js       |     100 |      100 |     100 |     100 |                   
  error.js         |     100 |      100 |     100 |     100 |                   
  ...view-props.js |     100 |      100 |     100 |     100 |                   
  globals.js       |     100 |      100 |     100 |     100 |                   
  next-url.js      |     100 |      100 |     100 |     100 |                   
  utils.js         |     100 |      100 |     100 |     100 |                   
  web-on-close.js  |     100 |      100 |     100 |     100 |                   
 ...spec-extension |     100 |      100 |     100 |     100 |                   
  fetch-event.js   |     100 |      100 |     100 |     100 |                   
  request.js       |     100 |      100 |     100 |     100 |                   
  response.js      |     100 |      100 |     100 |     100 |                   
  url-pattern.js   |     100 |      100 |     100 |     100 |                   
 ...nsion/adapters |     100 |      100 |     100 |     100 |                   
  headers.js       |     100 |      100 |     100 |     100 |                   
  reflect.js       |     100 |      100 |     100 |     100 |                   
  ...st-cookies.js |     100 |      100 |     100 |     100 |                   
 ...ental/testmode |   38.52 |        0 |       0 |   38.52 |                   
  context.js       |     100 |      100 |     100 |     100 |                   
  fetch.js         |       0 |        0 |       0 |       0 | 1-142             
  server-edge.js   |     100 |      100 |     100 |     100 |                   
 ...r/src/_N_E/src |     100 |      100 |     100 |     100 |                   
  middleware.ts    |     100 |      100 |     100 |     100 |                   
 ...ent/components |     100 |      100 |     100 |     100 |                   
  ...er-headers.ts |     100 |      100 |     100 |     100 |                   
 ...ccess-fallback |     100 |      100 |     100 |     100 |                   
  ...s-fallback.ts |     100 |      100 |     100 |     100 |                   
 ...ver/app-render |     100 |      100 |     100 |     100 |                   
  ...-rendering.ts |     100 |      100 |     100 |     100 |                   
 ...cache-handlers |     100 |      100 |     100 |     100 |                   
  ...t.external.ts |     100 |      100 |     100 |     100 |                   
 ...server/request |     100 |      100 |     100 |     100 |                   
  root-params.ts   |     100 |      100 |     100 |     100 |                   
 ...src/shared/lib |     100 |      100 |     100 |     100 |                   
  get-hostname.ts  |     100 |      100 |     100 |     100 |                   
  ...iant-error.ts |     100 |      100 |     100 |     100 |                   
  is-thenable.ts   |     100 |      100 |     100 |     100 |                   
  segment.ts       |     100 |      100 |     100 |     100 |                   
 ...hared/lib/i18n |     100 |      100 |     100 |     100 |                   
  ...ain-locale.ts |     100 |      100 |     100 |     100 |                   
  ...ocale-path.ts |     100 |      100 |     100 |     100 |                   
 .../lib/page-path |     100 |      100 |     100 |     100 |                   
  ...ding-slash.ts |     100 |      100 |     100 |     100 |                   
 ...b/router/utils |     100 |      100 |     100 |     100 |                   
  add-locale.ts    |     100 |      100 |     100 |     100 |                   
  ...ath-prefix.ts |     100 |      100 |     100 |     100 |                   
  ...ath-suffix.ts |     100 |      100 |     100 |     100 |                   
  app-paths.ts     |     100 |      100 |     100 |     100 |                   
  ...hname-info.ts |     100 |      100 |     100 |     100 |                   
  ...hname-info.ts |     100 |      100 |     100 |     100 |                   
  parse-path.ts    |     100 |      100 |     100 |     100 |                   
  ...has-prefix.ts |     100 |      100 |     100 |     100 |                   
  ...tivize-url.ts |     100 |      100 |     100 |     100 |                   
  ...ath-prefix.ts |     100 |      100 |     100 |     100 |                   
  ...ling-slash.ts |     100 |      100 |     100 |     100 |                   
 ...tP4M0J4P2lDXSx |       0 |        0 |       0 |       0 |                   
  ...ldManifest.js |       0 |        0 |       0 |       0 | 1                 
  _ssgManifest.js  |       0 |        0 |       0 |       0 | 1                 
 .../static/chunks |       0 |        0 |       0 |       0 |                   
  ...f290cc858c.js |       0 |        0 |       0 |       0 | 1                 
  ...1b87c2c3f8.js |       0 |        0 |       0 |       0 | 1                 
  ...50665eb2b5.js |       0 |        0 |       0 |       0 | 1                 
  ...dbc1df3066.js |       0 |        0 |       0 |       0 | 1                 
  ...7dddbba9fb.js |       0 |        0 |       0 |       0 | 1                 
  ...4960cdda7b.js |       0 |        0 |       0 |       0 | 1-54              
  ...bbb2af3165.js |       0 |        0 |       0 |       0 | 1                 
  ...687af20872.js |       0 |        0 |       0 |       0 | 1                 
  ...c01d3e6bfe.js |       0 |        0 |       0 |       0 | 1-5               
  ...1472046e21.js |       0 |        0 |       0 |       0 | 1                 
  ...c3119b01aa.js |       0 |        0 |       0 |       0 | 1                 
  ...27f54a98b8.js |       0 |        0 |       0 |       0 | 1                 
  ...d130431b0a.js |       0 |        0 |       0 |       0 | 1                 
  ...8a1d838b1b.js |       0 |        0 |       0 |       0 | 1                 
 ...tic/chunks/app |       0 |        0 |       0 |       0 |                   
  ...45581af6e4.js |       0 |        0 |       0 |       0 | 1                 
  ...b8175662de.js |       0 |        0 |       0 |       0 | 1                 
  ...152a7bdd7c.js |       0 |        0 |       0 |       0 | 1                 
  ...249ebc9743.js |       0 |        0 |       0 |       0 | 1                 
 ...app/_not-found |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...unks/app/about |       0 |        0 |       0 |       0 |                   
  ...9be75e0345.js |       0 |        0 |       0 |       0 | 1                 
 ...unks/app/admin |       0 |        0 |       0 |       0 |                   
  ...004dadcefd.js |       0 |        0 |       0 |       0 | 1                 
 .../app/analytics |       0 |        0 |       0 |       0 |                   
  ...aa737fc8ec.js |       0 |        0 |       0 |       0 | 1                 
 ...min/activities |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...activities/all |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nalytics/reset |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...s/[categoryId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...oryId]/migrate |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...erificationId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...admin/benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...dmin/companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nyId]/benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/verify |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...discover-users |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...yId]/locations |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...mpany-benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...rman-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ies/[reportId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...pany-reporters |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...pi/admin/users |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...users/[userId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...payment-status |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...benefit-trends |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...mpany-insights |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...s/company/[id] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...alytics/export |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ytics/overview |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../search-trends |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../top-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nalytics/track |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../user-insights |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../api/auth/csrf |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...uth/magic-link |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...pp/api/auth/me |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...i/auth/sign-in |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../auth/sign-out |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...i/auth/sign-up |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...update-profile |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...lidate-session |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...isputes/cancel |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...isputes/status |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../authorization |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...p/api/benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../api/companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../authorization |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../[id]/benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ts/[benefitId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../benefits/bulk |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/export |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...enefits/import |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...[id]/locations |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...tions/benefits |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ons/industries |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...app/api/health |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...pi/health/live |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...i/health/ready |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 .../health/simple |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ns/suggestions |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...issing-company |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...app/api/search |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...gs/[benefitId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...-verifications |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...i/user/profile |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  ...a796fcd6c1.js |       0 |        0 |       0 |       0 | 1                 
 ...uth/magic-link |       0 |        0 |       0 |       0 |                   
  ...4327ab5927.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  ...4de52e94f1.js |       0 |        0 |       0 |       0 | 1                 
 ...s/app/benefits |       0 |        0 |       0 |       0 |                   
  ...d9b7395569.js |       0 |        0 |       0 |       0 | 1                 
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  ...1393d14c12.js |       0 |        0 |       0 |       0 | 1                 
 .../app/dashboard |       0 |        0 |       0 |       0 |                   
  ...20b1e1dff0.js |       0 |        0 |       0 |       0 | 1                 
 ...ks/app/pricing |       0 |        0 |       0 |       0 |                   
  ...9be75e0345.js |       0 |        0 |       0 |       0 | 1                 
 ...s/app/rankings |       0 |        0 |       0 |       0 |                   
  ...31746038e2.js |       0 |        0 |       0 |       0 | 1                 
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  ...5828b8f33c.js |       0 |        0 |       0 |       0 | 1                 
 ...ks/app/sign-in |       0 |        0 |       0 |       0 |                   
  ...22b49e4d2c.js |       0 |        0 |       0 |       0 | 1                 
 ...ks/app/sign-up |       0 |        0 |       0 |       0 |                   
  ...f4f7a158de.js |       0 |        0 |       0 |       0 | 1                 
 ...verify-company |       0 |        0 |       0 |       0 |                   
  ...544dd18921.js |       0 |        0 |       0 |       0 | 1                 
 ...c/chunks/pages |       0 |        0 |       0 |       0 |                   
  ...71b16a04b8.js |       0 |        0 |       0 |       0 | 1                 
  ...d32cad7365.js |       0 |        0 |       0 |       0 | 1                 
 ...ll/.next/types |       0 |      100 |     100 |       0 |                   
  validator.ts     |       0 |      100 |     100 |       0 | 51-634            
 ...next/types/app |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...ypes/app/about |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...ypes/app/admin |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 .../app/analytics |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...min/activities |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...activities/all |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...nalytics/reset |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...fit-categories |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...s/[categoryId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...oryId]/migrate |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...nefit-rankings |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...moval-disputes |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...-verifications |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...erificationId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...admin/benefits |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...dmin/companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...nyId]/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...enefits/verify |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...discover-users |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...yId]/locations |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...mpany-benefits |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...rman-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...sing-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...ies/[reportId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...ompany-reports |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...pany-reporters |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...pi/admin/users |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...users/[userId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...payment-status |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...nefit-rankings |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...benefit-trends |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...mpany-insights |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...s/company/[id] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...alytics/export |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...ytics/overview |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../search-trends |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../top-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...nalytics/track |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../user-insights |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../api/auth/csrf |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...uth/magic-link |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...pp/api/auth/me |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...i/auth/sign-in |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../auth/sign-out |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...i/auth/sign-up |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...update-profile |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...lidate-session |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...verify-company |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...fit-categories |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...moval-disputes |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...isputes/cancel |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...isputes/status |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...-verifications |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...p/api/benefits |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../api/companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 .../[id]/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...ts/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 .../benefits/bulk |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...enefits/export |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...enefits/import |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...[id]/locations |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...tions/benefits |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...ons/industries |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...app/api/health |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...pi/health/live |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...i/health/ready |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 .../health/simple |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...ns/suggestions |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...sing-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...issing-company |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...aved-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...app/api/search |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...nefit-rankings |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...gs/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...-verifications |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...ompany-reports |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...i/user/profile |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...aved-companies |       0 |      100 |     100 |       0 |                   
  route.ts         |       0 |      100 |     100 |       0 | 2-341             
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...uth/magic-link |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...verify-company |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...s/app/benefits |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  page.ts          |       0 |        0 |       0 |       0 | 1-84              
 .../app/dashboard |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...es/app/pricing |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...s/app/rankings |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...aved-companies |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...es/app/sign-in |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...es/app/sign-up |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 ...verify-company |       0 |      100 |     100 |       0 |                   
  page.ts          |       0 |      100 |     100 |       0 | 2-78              
 workwell/src      |       0 |        0 |       0 |       0 |                   
  middleware.ts    |       0 |        0 |       0 |       0 | 1-148             
 workwell/src/app  |       0 |       50 |      50 |       0 |                   
  error.tsx        |       0 |      100 |     100 |       0 | 3-55              
  layout.tsx       |       0 |      100 |     100 |       0 | 2-39              
  not-found.tsx    |       0 |        0 |       0 |       0 | 1-41              
  page.tsx         |       0 |        0 |       0 |       0 | 1-32              
 .../src/app/about |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-143             
 .../src/app/admin |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-35              
 .../app/analytics |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-30              
 ...min/activities |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-35              
 ...activities/all |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-66              
 ...nalytics/reset |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-158             
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-101             
 ...s/[categoryId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-175             
 ...oryId]/migrate |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-93              
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-176             
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-218             
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-165             
 ...erificationId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-131             
 ...admin/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-228             
 ...dmin/companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-347             
 ...nyId]/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-188             
 ...enefits/verify |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-179             
 ...discover-users |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-36              
 ...yId]/locations |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-148             
 ...mpany-benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-217             
 ...rman-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-362             
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-82              
 ...ies/[reportId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-113             
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-165             
 ...pany-reporters |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-132             
 ...pi/admin/users |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-166             
 ...users/[userId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-88              
 ...payment-status |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-134             
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-234             
 ...benefit-trends |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-77              
 ...mpany-insights |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-103             
 ...s/company/[id] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-174             
 ...alytics/export |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-89              
 ...ytics/overview |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-47              
 .../search-trends |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-42              
 .../top-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-40              
 ...nalytics/track |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-70              
 .../user-insights |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-153             
 .../api/auth/csrf |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-39              
 ...uth/magic-link |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-62              
 ...pp/api/auth/me |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-35              
 ...i/auth/sign-in |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-70              
 .../auth/sign-out |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-16              
 ...i/auth/sign-up |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-72              
 ...update-profile |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-117             
 ...lidate-session |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-38              
 ...verify-company |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-90              
 ...fit-categories |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-29              
 ...moval-disputes |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-164             
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-120             
 ...isputes/cancel |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-114             
 ...isputes/status |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-125             
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-226             
 ...panyBenefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-43              
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-91              
 ...p/api/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-92              
 .../api/companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-95              
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-67              
 .../authorization |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-62              
 .../[id]/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-122             
 ...ts/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-39              
 .../benefits/bulk |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-54              
 ...enefits/export |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-81              
 ...enefits/import |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-79              
 ...[id]/locations |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-142             
 ...tions/benefits |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-38              
 ...ons/industries |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-33              
 ...app/api/health |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-225             
 ...pi/health/live |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-48              
 ...i/health/ready |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-139             
 .../health/simple |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-37              
 ...ns/suggestions |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-35              
 ...sing-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-142             
 ...issing-company |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-152             
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-147             
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-28              
 ...app/api/search |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-26              
 ...nefit-rankings |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-282             
 ...gs/[benefitId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-197             
 ...-verifications |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-48              
 ...ompany-reports |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-49              
 ...i/user/profile |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-110             
 ...aved-companies |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-157             
 ...es/[companyId] |       0 |        0 |       0 |       0 |                   
  route.ts         |       0 |        0 |       0 |       0 | 1-49              
 ...uth/magic-link |       0 |      100 |     100 |       0 |                   
  page.tsx         |       0 |      100 |     100 |       0 | 3-36              
 ...verify-company |       0 |      100 |     100 |       0 |                   
  page.tsx         |       0 |      100 |     100 |       0 | 3-144             
 ...c/app/benefits |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-21              
 ...companies/[id] |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-382             
 .../app/dashboard |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-40              
 ...rc/app/pricing |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-195             
 ...c/app/rankings |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-29              
 ...aved-companies |    70.9 |    73.68 |   14.28 |    70.9 |                   
  page.tsx         |    70.9 |    73.68 |   14.28 |    70.9 | ...28-148,189-192 
 ...rc/app/sign-in |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-35              
 ...rc/app/sign-up |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-35              
 ...verify-company |       0 |        0 |       0 |       0 |                   
  page.tsx         |       0 |        0 |       0 |       0 | 1-47              
 ...src/components |       0 |    96.15 |   96.15 |       0 |                   
  ...ctivities.tsx |       0 |      100 |     100 |       0 | 3-291             
  ...ics-reset.tsx |       0 |      100 |     100 |       0 | 3-301             
  ...ategories.tsx |       0 |      100 |     100 |       0 | 3-570             
  ...-rankings.tsx |       0 |      100 |     100 |       0 | 3-431             
  ...-disputes.tsx |       0 |      100 |     100 |       0 | 3-384             
  ...-benefits.tsx |       0 |      100 |     100 |       0 | 3-476             
  ...dashboard.tsx |       0 |      100 |     100 |       0 | 3-2359            
  ...dashboard.tsx |       0 |      100 |     100 |       0 | 3-652             
  ...ow-button.tsx |       0 |      100 |     100 |       0 | 3-55              
  ...selection.tsx |       0 |      100 |     100 |       0 | 3-558             
  ...anagement.tsx |       0 |      100 |     100 |       0 | 3-200             
  ...analytics.tsx |       0 |      100 |     100 |       0 | 3-306             
  ...t-ranking.tsx |       0 |      100 |     100 |       0 | 3-623             
  ...l-dispute.tsx |       0 |      100 |     100 |       0 | 3-291             
  ...on-counts.tsx |       0 |      100 |     100 |       0 | 3-63              
  ...ification.tsx |       0 |      100 |     100 |       0 | 3-202             
  ...fits-list.tsx |       0 |      100 |     100 |       0 | 3-226             
  company-card.tsx |       0 |        0 |       0 |       0 | 1-151             
  ...dashboard.tsx |       0 |      100 |     100 |       0 | 3-237             
  ...ny-search.tsx |       0 |      100 |     100 |       0 | 3-293             
  ...on-notice.tsx |       0 |      100 |     100 |       0 | 17-59             
  ...ification.tsx |       0 |      100 |     100 |       0 | 3-161             
  ...anagement.tsx |       0 |      100 |     100 |       0 | 3-286             
  header.tsx       |       0 |      100 |     100 |       0 | 3-222             
  ...ny-button.tsx |       0 |      100 |     100 |       0 | 3-98              
  ...de-prompt.tsx |       0 |      100 |     100 |       0 | 3-210             
 ...omponents/auth |       0 |      100 |     100 |       0 |                   
  ...ification.tsx |       0 |      100 |     100 |       0 | 3-214             
 ...nts/local-auth |       0 |      100 |     100 |       0 |                   
  sign-in-form.tsx |       0 |      100 |     100 |       0 | 3-112             
  sign-up-form.tsx |       0 |      100 |     100 |       0 | 3-162             
  user-button.tsx  |       0 |      100 |     100 |       0 | 3-231             
 .../components/ui |       0 |       70 |      70 |       0 |                   
  alert.tsx        |       0 |        0 |       0 |       0 | 1-56              
  badge.tsx        |       0 |      100 |     100 |       0 | 2-33              
  button.tsx       |       0 |        0 |       0 |       0 | 1-54              
  card.tsx         |       0 |        0 |       0 |       0 | 1-76              
  ...ion-input.tsx |       0 |      100 |     100 |       0 | 3-256             
  ...ion-input.tsx |       0 |      100 |     100 |       0 | 4-253             
  pagination.tsx   |       0 |      100 |     100 |       0 | 3-128             
  safe-date.tsx    |       0 |      100 |     100 |       0 | 3-85              
  ...ti-select.tsx |       0 |      100 |     100 |       0 | 3-177             
  select.tsx       |       0 |      100 |     100 |       0 | 3-146             
 ...well/src/hooks |       0 |      100 |     100 |       0 |                   
  ...horization.ts |       0 |      100 |     100 |       0 | 3-52              
 workwell/src/lib  |   15.69 |    60.75 |   38.67 |   15.69 |                   
  ...ity-logger.ts |       0 |        0 |       0 |       0 | 1-454             
  ...ss-control.ts |       0 |        0 |       0 |       0 | 1-145             
  ...cs-tracker.ts |       0 |        0 |       0 |       0 | 1-373             
  auth.ts          |       0 |        0 |       0 |       0 | 1-87              
  csrf.ts          |       0 |        0 |       0 |       0 | 1-81              
  database.ts      |   33.42 |    72.22 |   21.73 |   33.42 | ...04-408,459-699 
  ...-generator.ts |       0 |      100 |     100 |       0 | 98-416            
  ...ssociation.ts |       0 |        0 |       0 |       0 | 1-467             
  email.ts         |       0 |        0 |       0 |       0 | 1-175             
  ...validation.ts |       0 |        0 |       0 |       0 | 1-324             
  geocoding.ts     |       0 |      100 |     100 |       0 | 23-267            
  local-auth.ts    |       0 |        0 |       0 |       0 | 1-131             
  local-db.ts      |       0 |        0 |       0 |       0 | 1-156             
  ...middleware.ts |       0 |      100 |     100 |       0 | 2-212             
  ...malization.ts |   86.29 |    81.57 |     100 |   86.29 | ...10-217,303-305 
  logger.ts        |   36.12 |       80 |   33.33 |   36.12 | ...95-199,203-218 
  ...-link-auth.ts |       0 |        0 |       0 |       0 | 1-407             
  performance.ts   |       0 |        0 |       0 |       0 | 1-242             
  ...esql-cache.ts |   42.26 |    65.51 |   58.33 |   42.26 | ...57-262,266-303 
  ...rate-limit.ts |   79.61 |    60.86 |      70 |   79.61 | ...34-236,248-254 
  ...ql-session.ts |   32.89 |    33.33 |   36.84 |   32.89 | ...97-299,303-310 
  rate-limit.ts    |       0 |        0 |       0 |       0 | 1-125             
  ...on-storage.ts |       0 |      100 |     100 |       0 | 2-112             
  ...-discovery.ts |       0 |        0 |       0 |       0 | 1-209             
  utils.ts         |       0 |        0 |       0 |       0 | 1-63              
-------------------|---------|----------|---------|---------|-------------------
ERROR: Coverage for lines (14.87%) does not meet global threshold (90%)
ERROR: Coverage for functions (22.22%) does not meet global threshold (90%)
ERROR: Coverage for statements (14.87%) does not meet global threshold (90%)
ERROR: Coverage for branches (29.32%) does not meet global threshold (85%)
[0;31m❌ Unit tests failed with exit code 1[0m
[1;33m⚠️  Unit tests with coverage failed, retrying without coverage...[0m
[0;34mRunning: Unit tests (no coverage)[0m
Command: npm run test:unit -- --run --reporter=json --outputFile=test-results/unit-results.json

> benefitlens@0.1.0 test:unit
> vitest --config vitest.config.ts --run --reporter=json --outputFile=test-results/unit-results.json

JSON report written to /home/<USER>/git/workwell/test-results/unit-results.json
[0;32m✅ Unit tests (no coverage) completed successfully[0m
[0;32m✅ Unit tests completed in 10s (without coverage)[0m
[0;34mRunning Integration Tests...[0m
Starting test server...
[0;34mRunning: Integration tests[0m
Command: npm run test:integration -- --run --reporter=json --outputFile=test-results/integration-results.json

> benefitlens@0.1.0 test:integration
> vitest --config vitest.integration.config.ts --run --reporter=json --outputFile=test-results/integration-results.json

🧪 Starting integration test setup...
🧪 Starting real Next.js server for integration testing...
🧪 Preparing Next.js app...
🧪 Starting server on port 3001...
✅ Test server ready on http://localhost:3001
 ✓ Compiled /middleware in 179ms (114 modules)
 ○ Compiling /api/health ...
 ✓ Compiled /api/health in 679ms (304 modules)
[37m[2025-08-25T09:24:15.328Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.328Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.329Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.329Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.330Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT 1 as health_check",
  "duration": 13,
  "rowCount": 1
}
[37m[2025-08-25T09:24:15.331Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.333Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_cache($1, $2, $3)",
  "duration": 14,
  "rowCount": 1
}
[37m[2025-08-25T09:24:15.333Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.333Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT \n        COUNT(*) as company_count,\n        (SELECT COUNT(*) FROM benefits) as benefit...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:15.334Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:15.334Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 1,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.335Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT delete_cache($1)",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:15.335Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 2,
  "idleCount": 1,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.336Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT 1 as test",
  "duration": 1,
  "rowCount": 1
}
 GET /api/health 200 in 797ms
🧪 Setting up test database with real PostgreSQL connection...
🧪 Database connection successful: { time: 2025-08-25T09:24:15.352Z, version: 'PostgreSQL' }
🧪 Test database cleaned
🧪 Seeding comprehensive test data...
🧪 Comprehensive test database seeded with all scenarios
🧪 Test database setup complete
✅ Integration test setup complete
 ✓ Compiled /api/auth/me in 120ms (348 modules)
[37m[2025-08-25T09:24:15.855Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.855Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:15.858Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 11,
  "rowCount": 1
}
 GET /api/auth/me 200 in 184ms
 ✓ Compiled /api/auth/csrf in 75ms (351 modules)
[37m[2025-08-25T09:24:16.028Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.028Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.030Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 12,
  "rowCount": 1
}
[37m[2025-08-25T09:24:16.032Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.034Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_csrf_token($1, $2, $3)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/auth/csrf 200 in 134ms
 ✓ Compiled /api/user/profile in 246ms (367 modules)
[37m[2025-08-25T09:24:16.477Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.477Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.480Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:16.481Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.482Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM users WHERE email = $1 AND id != $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:16.482Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.492Z] DEBUG[0m: Database Query
Context: {
  "query": "UPDATE users\n       SET email = $1, first_name = $2, last_name = $3, updated_at = NOW()\n       WHERE...",
  "duration": 10,
  "rowCount": 1
}
[36m[2025-08-25T09:24:16.492Z] INFO[0m: User profile updated
Context: {
  "userId": "30303030-3030-3030-3030-303030303030",
  "email": "<EMAIL>",
  "changes": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
 PUT /api/user/profile 200 in 446ms
 GET /api/auth/me 401 in 23ms
[37m[2025-08-25T09:24:16.599Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.600Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 0
}
 GET /api/auth/me 401 in 19ms
 ✓ Compiled /api/companies in 84ms (371 modules)
[37m[2025-08-25T09:24:16.802Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.803Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.816Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 21,
  "rowCount": 127
}
 GET /api/companies?limit=3&page=1 200 in 192ms
[37m[2025-08-25T09:24:16.960Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:16.966Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 7,
  "rowCount": 22
}
 GET /api/companies?location=Berlin 200 in 117ms
 ✓ Compiled /api/companies/[id] in 72ms (373 modules)
[37m[2025-08-25T09:24:17.557Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.557Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.561Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 13,
  "rowCount": 1
}
 GET /api/companies/5c691a85-1101-4ea0-810f-f7fe2ed0bb90 200 in 583ms
[37m[2025-08-25T09:24:17.600Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.601Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/a2313897-00a7-4bf7-a0fd-fcaa26eff747 200 in 27ms
[37m[2025-08-25T09:24:17.628Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.629Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/companies/6b8523ca-0c34-4495-ae7c-58b7c187f8dd 200 in 18ms
[37m[2025-08-25T09:24:17.659Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.660Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/b6d9d93c-a9bb-41c4-be42-b1cfb89738f7 200 in 21ms
[37m[2025-08-25T09:24:17.685Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.686Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/00138327-c6fe-463b-8fce-30c76bd4c099 200 in 15ms
[37m[2025-08-25T09:24:17.709Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.710Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/25e7dca3-4f08-414f-baae-7b868102a0a2 200 in 17ms
[37m[2025-08-25T09:24:17.734Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.735Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/a32301a7-a61a-4919-8ea2-5258c5b1887d 200 in 15ms
[37m[2025-08-25T09:24:17.762Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.763Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/c91cf384-36cc-412f-bcab-6fda76e641c3 200 in 18ms
[37m[2025-08-25T09:24:17.786Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.787Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/49ba5831-32d0-4dcd-8d29-1c5d5b4676d5 200 in 13ms
[37m[2025-08-25T09:24:17.814Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.814Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/3da642a8-2604-4b14-b5c2-e54cbea674ba 200 in 16ms
[37m[2025-08-25T09:24:17.833Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.834Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/b6b1eb84-5f0e-4457-a6c8-0a3743233791 200 in 13ms
[37m[2025-08-25T09:24:17.861Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.862Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2 200 in 18ms
[37m[2025-08-25T09:24:17.886Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.887Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/000eb8b4-345d-4e1f-ae98-69318a0e97aa 200 in 15ms
[37m[2025-08-25T09:24:17.915Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.917Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/companies/d7adcc64-bfee-4ef3-999f-659dd00e2175 200 in 20ms
[37m[2025-08-25T09:24:17.949Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.950Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/companies/fa2034b0-c512-4be7-a1bc-700d1559a554 200 in 23ms
[37m[2025-08-25T09:24:17.996Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:17.997Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/c704a431-a900-4ccf-bd2b-a979f1c40ce0 200 in 38ms
[37m[2025-08-25T09:24:18.022Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.023Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/b388b36b-a6c5-4b3b-acb1-50663080811a 200 in 15ms
[37m[2025-08-25T09:24:18.109Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.110Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/329ca019-134c-40ab-b40b-0d6a79b65460 200 in 79ms
[37m[2025-08-25T09:24:18.199Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.200Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/b5780bae-a178-45db-aaea-52670c16f19f 200 in 72ms
[37m[2025-08-25T09:24:18.221Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.222Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd 200 in 14ms
[37m[2025-08-25T09:24:18.276Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.288Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 12,
  "rowCount": 127
}
 GET /api/companies?benefits=Test%20Health%20Insurance 200 in 38ms
[37m[2025-08-25T09:24:18.315Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.316Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd 200 in 15ms
[37m[2025-08-25T09:24:18.344Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.345Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee 200 in 18ms
[37m[2025-08-25T09:24:18.386Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.393Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 7,
  "rowCount": 9
}
 GET /api/companies?industry=Technology 200 in 20ms
[37m[2025-08-25T09:24:18.433Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.441Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 8,
  "rowCount": 52
}
 GET /api/companies?size=medium 200 in 23ms
[37m[2025-08-25T09:24:18.492Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.493Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT get_cache($1)",
  "duration": 1,
  "rowCount": 1
}
 GET /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd 200 in 21ms
 GET /api/companies/non-existent 404 in 15ms
[37m[2025-08-25T09:24:18.579Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:18.588Z] DEBUG[0m: Database Query
Context: {
  "query": "\n    SELECT\n      c.*,\n      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits...",
  "duration": 9,
  "rowCount": 1
}
 GET /api/companies?search=Test%20Corp 200 in 29ms
 ✓ Compiled /api/benefits in 342ms (375 modules)
[37m[2025-08-25T09:24:19.122Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.122Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.124Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        b.*,\n        bc.name as category_name,\n        bc.display_name as category_dis...",
  "duration": 9,
  "rowCount": 68
}
 GET /api/benefits 200 in 511ms
[37m[2025-08-25T09:24:19.176Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.177Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        b.*,\n        bc.name as category_name,\n        bc.display_name as category_dis...",
  "duration": 1,
  "rowCount": 2
}
 GET /api/benefits?category=test_health 200 in 15ms
[37m[2025-08-25T09:24:19.237Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.238Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        b.*,\n        bc.name as category_name,\n        bc.display_name as category_dis...",
  "duration": 1,
  "rowCount": 68
}
 GET /api/benefits?search=health 200 in 23ms
[37m[2025-08-25T09:24:19.287Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.289Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:19.290Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:19.292Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_csrf_token($1, $2, $3)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/auth/csrf 200 in 27ms
 ✓ Compiled /api/companies/[id]/benefits in 185ms (377 modules)
[37m[2025-08-25T09:24:20.012Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.013Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.015Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 18,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.016Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.017Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.018Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.029Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)\n     VALUES ($1, $2, $3...",
  "duration": 11,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.030Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.031Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)\n     VALUES ($1, $2, $3...",
  "duration": 1,
  "rowCount": 1
}
 POST /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd/benefits 200 in 731ms
[37m[2025-08-25T09:24:20.086Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.087Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.088Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.088Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.089Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.090Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT * FROM companies WHERE domain = $1",
  "duration": 1,
  "rowCount": 1
}
 POST /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd/benefits 403 in 29ms
 ✓ Compiled /api/companies/[id]/benefits/[benefitId] in 116ms (379 modules)
[37m[2025-08-25T09:24:20.748Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.748Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.750Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.751Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.751Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:20.752Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.761Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 9,
  "rowCount": 1
}
 DELETE /api/companies/dddddddd-dddd-dddd-dddd-dddddddddddd/benefits/55555555-5555-5555-5555-555555555555 200 in 641ms
 ✓ Compiled /api/user/benefit-rankings in 102ms (381 modules)
[37m[2025-08-25T09:24:20.997Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.997Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:20.999Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 9,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.000Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.001Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT id FROM benefits WHERE id = ANY($1)\n    ",
  "duration": 1,
  "rowCount": 3
}
[37m[2025-08-25T09:24:21.001Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.001Z] DEBUG[0m: Database Query
Context: {
  "query": "BEGIN",
  "duration": 0,
  "rowCount": null
}
[37m[2025-08-25T09:24:21.001Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.002Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM user_benefit_rankings WHERE user_id = $1",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:21.002Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.003Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)\n           VALUES ($1, $2, $3), ($1...",
  "duration": 1,
  "rowCount": 3
}
[37m[2025-08-25T09:24:21.003Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.004Z] DEBUG[0m: Database Query
Context: {
  "query": "COMMIT",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:21.004Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.005Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT ubr.ranking, ubr.created_at, ubr.updated_at,\n                b.id as benefit_id, b.name as be...",
  "duration": 1,
  "rowCount": 3
}
 POST /api/user/benefit-rankings 200 in 213ms
[37m[2025-08-25T09:24:21.057Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.058Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.059Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.059Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT id FROM benefits WHERE id = ANY($1)\n    ",
  "duration": 1,
  "rowCount": 3
}
[37m[2025-08-25T09:24:21.059Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.060Z] DEBUG[0m: Database Query
Context: {
  "query": "BEGIN",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:21.060Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.060Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM user_benefit_rankings WHERE user_id = $1",
  "duration": 0,
  "rowCount": 3
}
[37m[2025-08-25T09:24:21.061Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.061Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)\n           VALUES ($1, $2, $3), ($1...",
  "duration": 1,
  "rowCount": 3
}
[37m[2025-08-25T09:24:21.061Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.062Z] DEBUG[0m: Database Query
Context: {
  "query": "COMMIT",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:21.062Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.063Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT ubr.ranking, ubr.created_at, ubr.updated_at,\n                b.id as benefit_id, b.name as be...",
  "duration": 1,
  "rowCount": 3
}
 PUT /api/user/benefit-rankings 200 in 24ms
 ✓ Compiled /api/user/benefit-rankings/[benefitId] in 89ms (383 modules)
[37m[2025-08-25T09:24:21.706Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.706Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.709Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.710Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.710Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefits WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.710Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.711Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT benefit_id FROM user_benefit_rankings WHERE user_id = $1 AND ranking = $2 AND benefit_id != $...",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:21.711Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.721Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)\n       VALUES ($1, $2, $3)\n       O...",
  "duration": 10,
  "rowCount": 1
}
 POST /api/user/benefit-rankings/99999999-9999-9999-9999-999999999999 200 in 633ms
[37m[2025-08-25T09:24:21.781Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.783Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.783Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.784Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM user_benefit_rankings WHERE user_id = $1 AND benefit_id = $2",
  "duration": 1,
  "rowCount": 1
}
 DELETE /api/user/benefit-rankings/88888888-8888-8888-8888-888888888888 200 in 34ms
[37m[2025-08-25T09:24:21.835Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.837Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.837Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.838Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM user_benefit_rankings WHERE user_id = $1",
  "duration": 1,
  "rowCount": 3
}
 DELETE /api/user/benefit-rankings 200 in 26ms
[37m[2025-08-25T09:24:21.881Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.882Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:21.883Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:21.884Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        ubr.id,\n        ubr.user_id,\n        ubr.benefit_id,\n        ubr.ranking,\n    ...",
  "duration": 2,
  "rowCount": 3
}
 GET /api/user/benefit-rankings 200 in 19ms
 ✓ Compiled /api/missing-companies in 155ms (385 modules)
[37m[2025-08-25T09:24:22.300Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.300Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.303Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 11,
  "rowCount": 1
}
[37m[2025-08-25T09:24:22.304Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.305Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:22.305Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.315Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO missing_company_reports (\n        user_email,\n        email_domain,\n        first_name,\n...",
  "duration": 10,
  "rowCount": 1
}
[36m[2025-08-25T09:24:22.316Z] INFO[0m: Missing company report created
Context: {
  "reportId": "9fe4ea8e-1459-4586-bd2c-d391a20c9e10",
  "userId": "30303030-3030-3030-3030-303030303030",
  "userEmail": "<EMAIL>",
  "emailDomain": "testcorp.test"
}
 POST /api/missing-companies 201 in 405ms
[37m[2025-08-25T09:24:22.357Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.358Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:22.359Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.360Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:22.360Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.361Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO missing_company_reports (\n        user_email,\n        email_domain,\n        first_name,\n...",
  "duration": 1,
  "rowCount": 1
}
[36m[2025-08-25T09:24:22.361Z] INFO[0m: Missing company report created
Context: {
  "reportId": "cf6fb358-623a-4d21-af90-4af6ebb6f5f6",
  "userId": "30303030-3030-3030-3030-303030303030",
  "userEmail": "<EMAIL>",
  "emailDomain": "testcorp.test"
}
 POST /api/missing-companies 201 in 16ms
[37m[2025-08-25T09:24:22.381Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.382Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:22.382Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.383Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2",
  "duration": 1,
  "rowCount": 1
}
 POST /api/missing-companies 409 in 16ms
 ✓ Compiled /api/user/missing-company-reports in 270ms (387 modules)
[37m[2025-08-25T09:24:22.790Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.791Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.793Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 14,
  "rowCount": 1
}
[37m[2025-08-25T09:24:22.793Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:22.794Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        id,\n        user_email,\n        email_domain,\n        first_name,\n        last_name,\n...",
  "duration": 1,
  "rowCount": 1
}
[36m[2025-08-25T09:24:22.794Z] INFO[0m: User missing company reports retrieved
Context: {
  "userId": "30303030-3030-3030-3030-303030303030",
  "reportCount": 1
}
 GET /api/user/missing-company-reports 200 in 389ms
 ✓ Compiled /api/user/saved-companies in 235ms (389 modules)
[37m[2025-08-25T09:24:23.187Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.188Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.190Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:23.191Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.192Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id, name FROM companies WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
Attempting to save company: {
  userId: '50505050-5050-5050-5050-505050505050',
  companyId: 'dddddddd-dddd-dddd-dddd-dddddddddddd'
}
[37m[2025-08-25T09:24:23.193Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.203Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO saved_companies (user_id, company_id)\n         VALUES ($1, $2)\n         ON CONFLICT (use...",
  "duration": 10,
  "rowCount": 1
}
Insert result: { rowCount: 1 }
[36m[2025-08-25T09:24:23.203Z] INFO[0m: Company saved by user
Context: {
  "userId": "50505050-5050-5050-5050-505050505050",
  "companyId": "dddddddd-dddd-dddd-dddd-dddddddddddd",
  "savedId": "ebb12003-f0e2-4484-aea0-953770d63002"
}
 POST /api/user/saved-companies 200 in 375ms
 ✓ Compiled /api/user/saved-companies/[companyId] in 114ms (391 modules)
[37m[2025-08-25T09:24:23.832Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.832Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.834Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 9,
  "rowCount": 1
}
[37m[2025-08-25T09:24:23.834Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.843Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM saved_companies WHERE user_id = $1 AND company_id = $2 RETURNING id",
  "duration": 9,
  "rowCount": 1
}
[36m[2025-08-25T09:24:23.843Z] INFO[0m: Company unsaved by user
Context: {
  "userId": "30303030-3030-3030-3030-303030303030",
  "companyId": "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee",
  "removedId": "f2d39d49-f6b6-47ae-8575-69ea4f976d4b"
}
 DELETE /api/user/saved-companies/eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee 200 in 607ms
[37m[2025-08-25T09:24:23.897Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.898Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:23.899Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.901Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT \n        sc.id as saved_id,\n        sc.created_at as saved_at,\n        c.id,\n        c.name,\n...",
  "duration": 2,
  "rowCount": 2
}
[36m[2025-08-25T09:24:23.902Z] INFO[0m: User saved companies retrieved
Context: {
  "userId": "30303030-3030-3030-3030-303030303030",
  "companyCount": 2
}
 GET /api/user/saved-companies 200 in 24ms
[37m[2025-08-25T09:24:23.943Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.944Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:23.945Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.945Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id, name FROM companies WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
Attempting to save company: {
  userId: '30303030-3030-3030-3030-303030303030',
  companyId: 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'
}
[37m[2025-08-25T09:24:23.946Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:23.947Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO saved_companies (user_id, company_id)\n         VALUES ($1, $2)\n         ON CONFLICT (use...",
  "duration": 2,
  "rowCount": 0
}
Insert result: { rowCount: 0 }
Company already saved, returning 409
 POST /api/user/saved-companies 409 in 19ms
 ✓ Compiled /api/analytics/user-insights in 121ms (393 modules)
[37m[2025-08-25T09:24:24.184Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.185Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.188Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 11,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.188Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.190Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM user_benefit_rankings WHERE user_id = $1",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.190Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.191Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM saved_companies WHERE user_id = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.192Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.192Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM benefit_verifications WHERE user_id = $1",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.193Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.194Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM missing_company_reports WHERE user_email = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.194Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.196Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT \n          b.id,\n          b.name,\n          b.icon,\n          bc.display_name as category,\n ...",
  "duration": 2,
  "rowCount": 3
}
[37m[2025-08-25T09:24:24.196Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.197Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT \n            c.name,\n            c.domain,\n            c.industry,\n            c.size,\n      ...",
  "duration": 1,
  "rowCount": 1
}
[36m[2025-08-25T09:24:24.198Z] INFO[0m: User insights retrieved
Context: {
  "userId": "40404040-4040-4040-4040-404040404040",
  "isPremium": true,
  "activityLevel": 4
}
 GET /api/analytics/user-insights 200 in 229ms
[37m[2025-08-25T09:24:24.251Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.252Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.252Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.252Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM user_benefit_rankings WHERE user_id = $1",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.253Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.253Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM saved_companies WHERE user_id = $1",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.254Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.254Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM benefit_verifications WHERE user_id = $1",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.254Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.255Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as count FROM missing_company_reports WHERE user_email = $1",
  "duration": 1,
  "rowCount": 1
}
[36m[2025-08-25T09:24:24.255Z] INFO[0m: User insights retrieved
Context: {
  "userId": "30303030-3030-3030-3030-303030303030",
  "isPremium": false,
  "activityLevel": 5
}
 GET /api/analytics/user-insights 200 in 20ms
[37m[2025-08-25T09:24:24.298Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.299Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.300Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.302Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_csrf_token($1, $2, $3)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/auth/csrf 200 in 19ms
 ✓ Compiled /api/analytics/track in 265ms (396 modules)
[37m[2025-08-25T09:24:24.692Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.692Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.695Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 9,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.696Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.699Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      INSERT INTO company_page_views (\n        company_id, user_id, session_id, ip_address, user_ag...",
  "duration": 3,
  "rowCount": 1
}
[37m[2025-08-25T09:24:24.699Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.702Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT update_company_analytics_summary($1, CURRENT_DATE)\n    ",
  "duration": 3,
  "rowCount": 1
}
 POST /api/analytics/track 200 in 392ms
Generic analytics event tracked: page_view { page: '/benefits', anonymous: true }
 POST /api/analytics/track 200 in 17ms
 ✓ Compiled /api/analytics/benefit-rankings in 126ms (400 modules)
[37m[2025-08-25T09:24:24.998Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:24.999Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.001Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.001Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.002Z] DEBUG[0m: Database Query
Context: {
  "query": "\n        CREATE TABLE IF NOT EXISTS user_benefit_rankings (\n          id UUID PRIMARY KEY DEFAULT uu...",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:25.002Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.003Z] DEBUG[0m: Database Query
Context: {
  "query": "CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:25.003Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.003Z] DEBUG[0m: Database Query
Context: {
  "query": "CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id)...",
  "duration": 0,
  "rowCount": null
}
[37m[2025-08-25T09:24:25.004Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.004Z] DEBUG[0m: Database Query
Context: {
  "query": "CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);",
  "duration": 0,
  "rowCount": null
}
[37m[2025-08-25T09:24:25.004Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.005Z] DEBUG[0m: Database Query
Context: {
  "query": "CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at)...",
  "duration": 1,
  "rowCount": null
}
[37m[2025-08-25T09:24:25.005Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.007Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        b.id as benefit_id,\n        b.name as benefit_name,\n        bc.name as categor...",
  "duration": 2,
  "rowCount": 14
}
[37m[2025-08-25T09:24:25.008Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.009Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        DATE(ubr.updated_at) as date,\n        b.name as benefit_name,\n        AVG(ubr....",
  "duration": 1,
  "rowCount": 14
}
[37m[2025-08-25T09:24:25.009Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.010Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      SELECT\n        bc.name as category,\n        bc.display_name as category_display_name,\n       ...",
  "duration": 1,
  "rowCount": 6
}
[37m[2025-08-25T09:24:25.010Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.011Z] DEBUG[0m: Database Query
Context: {
  "query": "\n      WITH current_period AS (\n        SELECT\n          benefit_id,\n          AVG(ranking::numeric)...",
  "duration": 1,
  "rowCount": 0
}
 GET /api/analytics/benefit-rankings 200 in 244ms
[37m[2025-08-25T09:24:25.067Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.068Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.069Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.071Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_csrf_token($1, $2, $3)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/auth/csrf 200 in 27ms
 ✓ Compiled /api/benefit-verifications in 147ms (402 modules)
[37m[2025-08-25T09:24:25.354Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.354Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.356Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 10,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.357Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.357Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.358Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.358Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.359Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.370Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)\n             VALUES ($1...",
  "duration": 12,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.371Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.372Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.372Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.373Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.373Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.374Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.374Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.375Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.375Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.376Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 1,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Mental Health Support at Test Corp by John Doe
[37m[2025-08-25T09:24:25.377Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.377Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.377Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:25.377Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 0,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 299ms
[37m[2025-08-25T09:24:25.426Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.427Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.427Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.427Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.428Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.428Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.429Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.429Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)\n             VALUES ($1...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.430Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.430Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.430Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.430Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.431Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.431Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.431Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.432Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.432Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.433Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 1,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Health Insurance at Test Startup by Bob Johnson
[37m[2025-08-25T09:24:25.433Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.434Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.434Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:25.434Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 0,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 29ms
[37m[2025-08-25T09:24:25.455Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.456Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.456Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.457Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.457Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.457Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.457Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.458Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.458Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.459Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 1,
  "rowCount": 1
}
 POST /api/benefit-verifications 409 in 15ms
[37m[2025-08-25T09:24:25.504Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.505Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.506Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.506Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.507Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.507Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.508Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.509Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)\n             VALUES ($1...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.509Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.510Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.510Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.510Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.510Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.511Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.511Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.512Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.512Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.513Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 1,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Health Insurance at Test Startup by Bob Johnson
[37m[2025-08-25T09:24:25.513Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.513Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:25.513Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:25.513Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 0,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 27ms
 ✓ Compiled /api/user/benefit-verifications in 317ms (404 modules)
[37m[2025-08-25T09:24:25.950Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.950Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.953Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 18,
  "rowCount": 1
}
[37m[2025-08-25T09:24:25.953Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:25.956Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        bv.id,\n        bv.status,\n        bv.comment,\n        bv.created_at,\n        c.name a...",
  "duration": 3,
  "rowCount": 1
}
 GET /api/user/benefit-verifications 200 in 439ms
[37m[2025-08-25T09:24:26.021Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.022Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.022Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.023Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.024Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.024Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.025Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.025Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.026Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.026Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:26.026Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.028Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.028Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.029Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.029Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.031Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 2,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Health Insurance at Test Corp by John Doe
[37m[2025-08-25T09:24:26.031Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.031Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:26.032Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:26.032Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 0,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 37ms
 ✓ Compiled /api/admin/benefit-verifications in 149ms (406 modules)
[37m[2025-08-25T09:24:26.289Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.289Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.291Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 7,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.291Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.293Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        bv.id,\n        bv.status,\n        bv.comment,\n        bv.created_at,\n        u.email ...",
  "duration": 2,
  "rowCount": 9
}
[37m[2025-08-25T09:24:26.294Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.294Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as total\n       FROM benefit_verifications bv\n       JOIN company_benefits cb ON bv....",
  "duration": 0,
  "rowCount": 1
}
 GET /api/admin/benefit-verifications 200 in 254ms
[37m[2025-08-25T09:24:26.359Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.360Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.360Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.361Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.361Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.362Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.362Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.362Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.362Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.363Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:26.363Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.364Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.365Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.365Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 0,
  "rowCount": 1
}
[37m[2025-08-25T09:24:26.365Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.366Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 1,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Health Insurance at Test Industries by Jane Smith
[37m[2025-08-25T09:24:26.367Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:26.367Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:26.367Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:26.367Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 0,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 43ms
 ✓ Compiled /api/admin/benefit-verifications/[verificationId] in 326ms (408 modules)
[37m[2025-08-25T09:24:27.180Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.181Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.183Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 12,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.184Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.185Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.185Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.195Z] DEBUG[0m: Database Query
Context: {
  "query": "UPDATE benefit_verifications\n       SET status = $1\n       WHERE id = $2\n       RETURNING *",
  "duration": 9,
  "rowCount": 1
}
 PUT /api/admin/benefit-verifications/793cc246-5b39-4a08-8a54-ae820c800cdd 200 in 821ms
[37m[2025-08-25T09:24:27.260Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.262Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.262Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.263Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.264Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.265Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.265Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.266Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT cb.company_id, c.domain, c.name\n       FROM company_benefits cb\n       JOIN companies c ON cb...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.267Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.267Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:27.267Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.269Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)\n       VALUES ($1, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.269Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.270Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT\n        c.id as company_id, c.name as company_name,\n        b.id as benefit_id, b.name as ben...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.271Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.272Z] DEBUG[0m: Database Query
Context: {
  "query": "INSERT INTO activity_log (\n        event_type, event_description, user_id, user_email, user_name,\n  ...",
  "duration": 1,
  "rowCount": 1
}
✅ Activity logged: benefit_verified - Benefit verified: Test Remote Work at Test Corp by John Doe
[37m[2025-08-25T09:24:27.272Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.273Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT status FROM benefit_verifications WHERE company_benefit_id = $1",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:27.273Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[31m[2025-08-25T09:24:27.274Z] ERROR[0m: Database Error
Context: {
  "query": "UPDATE company_benefits SET is_verified = $1, verification_count = $2 WHERE id = $3",
  "error": {
    "length": 149,
    "name": "error",
    "severity": "ERROR",
    "code": "42703",
    "position": "47",
    "file": "analyze.c",
    "line": "2515",
    "routine": "transformUpdateTargetList"
  },
  "duration": 1,
  "params": [
    false,
    0,
    null
  ]
}
Stack: error: column "verification_count" of relation "company_benefits" does not exist
    at /home/<USER>/git/workwell/node_modules/pg/lib/client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async AppRouteRouteModule.do (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:38696)
    at async AppRouteRouteModule.handle (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:5:45978)
    at async responseGenerator (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:206:38)
    at async AppRouteRouteModule.handleResponse (/home/<USER>/git/workwell/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:1:187565)
    at async handleResponse (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:268:32)
    at async handler (webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbenefit-verifications%2Froute&page=%2Fapi%2Fbenefit-verifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbenefit-verifications%2Froute.ts&appDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frgarcia%2Fgit%2Fworkwell&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!:320:13)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1422:9)
    at async DevServer.renderPageComponent (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1474:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1514:32)
    at async DevServer.pipeImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:1025:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/next-server.js:393:17)
    at async DevServer.handleRequestImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/base-server.js:916:17)
    at async /home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:399:20
    at async Span.traceAsyncFn (/home/<USER>/git/workwell/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/dev/next-dev-server.js:395:24)
    at async invokeRender (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:240:21)
    at async handleRequest (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:437:24)
    at async NextCustomServer.requestHandlerImpl (/home/<USER>/git/workwell/node_modules/next/dist/server/lib/router-server.js:485:13)
    at async Server.<anonymous> (/home/<USER>/git/workwell/src/__tests__/integration/setup.ts:50:7)
Error updating company benefit status: error: column "verification_count" of relation "company_benefits" does not exist
    at async query (webpack-internal:///(rsc)/./src/lib/local-db.ts:65:24)
    at async updateCompanyBenefitStatus (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:31:9)
    at async POST (webpack-internal:///(rsc)/./src/app/api/benefit-verifications/route.ts:198:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7) {
  length: 149,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '47',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'analyze.c',
  line: '2515',
  routine: 'transformUpdateTargetList'
}
 POST /api/benefit-verifications 201 in 44ms
[37m[2025-08-25T09:24:27.309Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.310Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 2,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.310Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.311Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM benefit_verifications WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.311Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.312Z] DEBUG[0m: Database Query
Context: {
  "query": "UPDATE benefit_verifications\n       SET status = $1\n       WHERE id = $2\n       RETURNING *",
  "duration": 1,
  "rowCount": 1
}
 PUT /api/admin/benefit-verifications/e61d2264-1f4e-40aa-8408-9dace2529bb1 200 in 29ms
 ✓ Compiled /api/admin/missing-companies in 407ms (410 modules)
[37m[2025-08-25T09:24:27.959Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.959Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.963Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 37,
  "rowCount": 1
}
[37m[2025-08-25T09:24:27.964Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.965Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT \n        id,\n        user_email,\n        email_domain,\n        first_name,\n        last_name,...",
  "duration": 2,
  "rowCount": 10
}
[37m[2025-08-25T09:24:27.965Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:27.966Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT COUNT(*) as total\n       FROM missing_company_reports\n       ",
  "duration": 1,
  "rowCount": 1
}
 GET /api/admin/missing-companies 200 in 628ms
 ✓ Compiled /api/admin/missing-companies/[reportId] in 199ms (412 modules)
[37m[2025-08-25T09:24:28.772Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:28.772Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:28.774Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 8,
  "rowCount": 1
}
[37m[2025-08-25T09:24:28.775Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:28.776Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT id FROM missing_company_reports WHERE id = $1",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:28.776Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:28.786Z] DEBUG[0m: Database Query
Context: {
  "query": "UPDATE missing_company_reports\n       SET status = $1, admin_notes = $2, company_id = $3, updated_at...",
  "duration": 10,
  "rowCount": 1
}
 PUT /api/admin/missing-companies/11111111-1111-1111-1111-111111111111 200 in 772ms
 ✓ Compiled /api/admin/analytics/reset in 392ms (414 modules)
[37m[2025-08-25T09:24:29.335Z] DEBUG[0m: Database connection established
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.335Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.338Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 11,
  "rowCount": 1
}
[37m[2025-08-25T09:24:29.338Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.348Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM company_page_views",
  "duration": 10,
  "rowCount": 4
}
[37m[2025-08-25T09:24:29.349Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.349Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM search_queries",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:29.350Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.351Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM benefit_search_interactions",
  "duration": 1,
  "rowCount": 0
}
[37m[2025-08-25T09:24:29.351Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.351Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM daily_analytics_summary",
  "duration": 0,
  "rowCount": 0
}
[37m[2025-08-25T09:24:29.352Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.352Z] DEBUG[0m: Database Query
Context: {
  "query": "DELETE FROM company_analytics_summary",
  "duration": 0,
  "rowCount": 0
}
 POST /api/admin/analytics/reset 200 in 542ms
[37m[2025-08-25T09:24:29.408Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.409Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
Error retrieving benefit verifications for admin: Error: Admin access required
    at requireAdmin (webpack-internal:///(rsc)/./src/lib/auth.ts:41:15)
    at async GET (webpack-internal:///(rsc)/./src/app/api/admin/benefit-verifications/route.ts:20:9)
    at async Server.<anonymous> (src/__tests__/integration/setup.ts:50:7)
 GET /api/admin/benefit-verifications 403 in 26ms
[37m[2025-08-25T09:24:29.452Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.453Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
[37m[2025-08-25T09:24:29.454Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.456Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT set_csrf_token($1, $2, $3)",
  "duration": 2,
  "rowCount": 1
}
 GET /api/auth/csrf 200 in 19ms
[37m[2025-08-25T09:24:29.487Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.488Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
 POST /api/user/benefit-rankings 400 in 25ms
[37m[2025-08-25T09:24:29.532Z] DEBUG[0m: Database connection acquired from pool
Context: {
  "totalCount": 1,
  "idleCount": 0,
  "waitingCount": 0
}
[37m[2025-08-25T09:24:29.533Z] DEBUG[0m: Database Query
Context: {
  "query": "SELECT s.user_id, s.expires_at, u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, ...",
  "duration": 1,
  "rowCount": 1
}
 POST /api/missing-companies 400 in 17ms
 GET /api/companies/invalid-id-format 404 in 30ms
Generic analytics event tracked: test_event { test: true }
 POST /api/analytics/track 200 in 47ms
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
 POST /api/analytics/track 200 in 98ms
 POST /api/analytics/track 200 in 98ms
 POST /api/analytics/track 200 in 98ms
 POST /api/analytics/track 200 in 98ms
 POST /api/analytics/track 200 in 98ms
 POST /api/analytics/track 200 in 84ms
 POST /api/analytics/track 200 in 84ms
 POST /api/analytics/track 200 in 84ms
 POST /api/analytics/track 200 in 84ms
 POST /api/analytics/track 200 in 84ms
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
 POST /api/analytics/track 200 in 108ms
 POST /api/analytics/track 200 in 108ms
 POST /api/analytics/track 200 in 108ms
 POST /api/analytics/track 200 in 108ms
 POST /api/analytics/track 200 in 107ms
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
Generic analytics event tracked: test_event { test: true }
 POST /api/analytics/track 200 in 110ms
 POST /api/analytics/track 200 in 111ms
 POST /api/analytics/track 200 in 112ms
 POST /api/analytics/track 200 in 112ms
JSON report written to /home/<USER>/git/workwell/test-results/integration-results.json
🧪 Starting integration test teardown...
🧪 Test database cleaned
🧪 Stopping test server...
✅ Test server stopped
🧪 Closing Next.js app...
✅ Next.js app closed
✅ Integration test teardown complete
close timed out after 10000ms
Tests closed successfully but something prevents Vite server from exiting
You can try to identify the cause by enabling "hanging-process" reporter. See https://vitest.dev/config/#reporters
[0;32m✅ Integration tests completed successfully[0m
[0;32m✅ Integration tests completed in 38s[0m
[0;34mRunning E2E Tests...[0m
[0;34mRunning: E2E tests[0m
Command: npm run test:e2e -- --reporter=json --output-dir=test-results/e2e

> benefitlens@0.1.0 test:e2e
> playwright test --reporter=json --output-dir=test-results/e2e

error: unknown option '--output-dir=test-results/e2e'
[0;31m❌ E2E tests failed with exit code 1[0m
[0;31m❌ E2E tests failed[0m
[0;34mGenerating test reports...[0m
[0;32m✅ Test reports generated in test-results/[0m
[0;34mCleaning up...[0m
[0;32m✅ Cleanup completed[0m

[0;34m📊 Test Suite Summary[0m
Total duration: 70s
Results directory: test-results
[0;31m❌ Some tests failed: e2e[0m
[0;31m🚫 BenefitLens is NOT ready for production deployment[0m
[0;34mCleaning up...[0m
[0;32m✅ Cleanup completed[0m
