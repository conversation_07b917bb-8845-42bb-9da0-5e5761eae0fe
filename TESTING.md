# Testing Guide

This guide covers how to test the Workwell application functionality.

## Prerequisites for Testing

1. Set up the database with seed data using `npm run dev:start`
2. Configure magic link authentication (local SMTP with MailHog)
3. Have test company email addresses ready (see docs/LOCAL_DEVELOPMENT.md for test accounts)

## Manual Testing Checklist

### 1. Homepage and Search Functionality

- [ ] Homepage loads correctly
- [ ] Search form is visible and functional
- [ ] Basic search without filters works
- [ ] Location filter works
- [ ] Company size filter works
- [ ] Industry filter works
- [ ] Benefits filter works (try "Wellsport", "sabbatical")
- [ ] Search results display correctly
- [ ] Company cards show proper information

### 2. Company Detail Pages

- [ ] Company detail page loads from search results
- [ ] Company information displays correctly
- [ ] Benefits are categorized properly
- [ ] Verified vs unverified benefits are distinguished
- [ ] Benefit verification components are visible

### 3. Authentication Flow

- [ ] Sign-up page works
- [ ] Sign-in page works
- [ ] User can sign out
- [ ] Protected routes redirect to sign-in
- [ ] Dashboard is accessible after sign-in

### 4. Company Verification

- [ ] User with company email can access dashboard
- [ ] Company verification button works
- [ ] Verification status updates correctly
- [ ] Users without matching company see appropriate message

### 5. Benefit Verification

- [ ] Users can confirm benefits
- [ ] Users can dispute benefits with comments
- [ ] Verification status updates in real-time
- [ ] Non-authenticated users see sign-in prompt

### 6. Navigation and UI

- [ ] Header navigation works
- [ ] Mobile responsiveness
- [ ] Benefits page loads and filters work
- [ ] About page displays correctly
- [ ] All buttons and links function

## API Testing

### Test the following endpoints:

```bash
# Get companies
curl "http://localhost:3000/api/companies"

# Search companies
curl "http://localhost:3000/api/companies?search=SAP"

# Filter by benefits
curl "http://localhost:3000/api/companies?benefits=Wellsport"

# Get benefits
curl "http://localhost:3000/api/benefits"

# Search
curl "http://localhost:3000/api/search?q=Technology"
```

## Database Testing

### Verify the following data exists:

1. **Companies**: SAP, Deutsche Bank, Accenture, PwC
2. **Benefits**: Health Insurance, Wellsport, Sabbatical Leave, etc.
3. **Company Benefits**: Proper associations between companies and benefits
4. **Verification Status**: Some benefits should be verified, others pending

### Test SQL Queries:

```sql
-- Check companies
SELECT * FROM companies LIMIT 5;

-- Check benefits
SELECT * FROM benefits LIMIT 10;

-- Check company benefits with joins
SELECT c.name, b.name, cb.is_verified 
FROM companies c
JOIN company_benefits cb ON c.id = cb.company_id
JOIN benefits b ON cb.benefit_id = b.id
LIMIT 10;
```

## User Scenarios

### Scenario 1: Job Seeker Looking for Wellsport

1. Visit homepage
2. Search for "Wellsport" in benefits filter
3. Verify companies offering Wellsport appear
4. Click on a company (e.g., SAP)
5. Verify Wellsport benefit is listed
6. Check verification status

### Scenario 2: Company Representative

1. Sign up with company email (e.g., <EMAIL>)
2. Go to dashboard
3. Verify company appears
4. Click "Verify Company"
5. Check verification status updates

### Scenario 3: Benefit Verification

1. Sign in as any user
2. Go to company detail page
3. Find an unverified benefit
4. Click "Confirm" or "Dispute"
5. Verify the action completes
6. Check if benefit status updates

## Performance Testing

### Check the following:

- [ ] Page load times under 3 seconds
- [ ] Search results appear quickly
- [ ] Images load properly
- [ ] No console errors
- [ ] Mobile performance is acceptable

## Browser Testing

Test in the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

## Error Handling Testing

### Test error scenarios:

- [ ] Invalid company ID in URL
- [ ] Network errors during API calls
- [ ] Invalid form submissions
- [ ] Unauthorized access attempts
- [ ] Database connection issues

## Security Testing

### Verify:

- [ ] API routes require proper authentication
- [ ] Users can only modify their own company data
- [ ] SQL injection protection
- [ ] XSS protection
- [ ] CSRF protection

## Accessibility Testing

### Check:

- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets standards
- [ ] Alt text for images
- [ ] Proper heading structure

## Test Data

### Sample Companies:
- SAP (Frankfurt, Enterprise, Technology)
- Deutsche Bank (Frankfurt, Enterprise, Financial Services)
- Accenture (Frankfurt, Enterprise, Consulting)

### Sample Benefits:
- Wellsport (Wellness)
- Health Insurance (Health)
- Sabbatical Leave (Time Off)
- Stock Options (Financial)

### Test Email Domains:
- sap.com
- db.com
- accenture.com
- pwc.com

## Automated Testing (Future)

Consider implementing:
- Unit tests for utility functions
- Integration tests for API routes
- E2E tests with Playwright or Cypress
- Component tests with React Testing Library
