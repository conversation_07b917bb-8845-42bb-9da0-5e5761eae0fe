/**
 * Admin Workflow E2E Tests
 * Tests complete admin workflows and management features
 */

import { test, expect, Page } from '@playwright/test'

// Helper functions
async function signInAdmin(page: Page, email: string = '<EMAIL>') {
  await page.goto('/sign-in')
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // Mock admin verification
  await page.goto('/auth/magic-link?token=mock-admin-token&email=' + encodeURIComponent(email))
  await page.waitForURL(/\/(admin|dashboard)/)
}

async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle')
  await page.waitForTimeout(1000)
}

test.describe('Admin Workflow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.context().clearCookies()
  })

  test('Admin Company Management Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to admin panel
    await page.goto('/admin')
    await waitForPageLoad(page)
    
    // Should see admin dashboard
    await expect(page.locator('text=Admin Dashboard')).toBeVisible()
    
    // 3. Navigate to company management
    await page.click('text=Companies')
    await waitForPageLoad(page)
    
    // Should see companies list
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    await expect(page.locator('text=E2E Industries')).toBeVisible()
    await expect(page.locator('text=E2E Startup')).toBeVisible()
    
    // 4. Create new company
    await page.click('button:has-text("Add Company")')
    await page.fill('input[name="name"]', 'E2E New Company')
    await page.fill('input[name="domain"]', 'newcompany.e2e')
    await page.selectOption('select[name="industry"]', 'Technology')
    await page.selectOption('select[name="size"]', '50-100')
    await page.fill('textarea[name="description"]', 'New company for E2E testing')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see success message and new company
    await expect(page.locator('text=Company created successfully')).toBeVisible()
    await expect(page.locator('text=E2E New Company')).toBeVisible()
    
    // 5. Edit company
    await page.click('button[aria-label="Edit E2E New Company"]')
    await page.fill('input[name="name"]', 'E2E Updated Company')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated company name
    await expect(page.locator('text=E2E Updated Company')).toBeVisible()
    
    // 6. Verify company
    await page.click('button[aria-label="Verify E2E Updated Company"]')
    await page.click('button:has-text("Confirm Verification")')
    await waitForPageLoad(page)
    
    // Should see verified status
    await expect(page.locator('text=Verified').first()).toBeVisible()
  })

  test('Admin Benefit Management Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to benefit management
    await page.goto('/admin/benefits')
    await waitForPageLoad(page)
    
    // Should see benefits list
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).toBeVisible()
    
    // 3. Create new benefit
    await page.click('button:has-text("Add Benefit")')
    await page.fill('input[name="name"]', 'E2E New Benefit')
    await page.fill('textarea[name="description"]', 'New benefit for E2E testing')
    await page.selectOption('select[name="category"]', 'e2e-cat-1')
    await page.fill('input[name="icon"]', '🎯')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see success message and new benefit
    await expect(page.locator('text=Benefit created successfully')).toBeVisible()
    await expect(page.locator('text=E2E New Benefit')).toBeVisible()
    
    // 4. Edit benefit
    await page.click('button[aria-label="Edit E2E New Benefit"]')
    await page.fill('input[name="name"]', 'E2E Updated Benefit')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated benefit name
    await expect(page.locator('text=E2E Updated Benefit')).toBeVisible()
    
    // 5. Deactivate benefit
    await page.click('button[aria-label="Deactivate E2E Updated Benefit"]')
    await page.click('button:has-text("Confirm Deactivation")')
    await waitForPageLoad(page)
    
    // Should see inactive status
    await expect(page.locator('text=Inactive').first()).toBeVisible()
  })

  test('Admin User Management Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to user management
    await page.goto('/admin/users')
    await waitForPageLoad(page)
    
    // Should see users list
    await expect(page.locator('text=user1@techcorp.e2e')).toBeVisible()
    await expect(page.locator('text=user2@industries.e2e')).toBeVisible()
    
    // 3. Search for specific user
    await page.fill('input[placeholder*="Search users"]', 'user1@techcorp.e2e')
    await waitForPageLoad(page)
    
    // Should see filtered results
    await expect(page.locator('text=user1@techcorp.e2e')).toBeVisible()
    await expect(page.locator('text=user2@industries.e2e')).not.toBeVisible()
    
    // 4. View user details
    await page.click('text=user1@techcorp.e2e')
    await waitForPageLoad(page)
    
    // Should see user profile
    await expect(page.locator('text=John Doe')).toBeVisible()
    await expect(page.locator('text=techcorp.e2e')).toBeVisible()
    
    // 5. Update user information
    await page.click('button:has-text("Edit User")')
    await page.fill('input[name="firstName"]', 'Johnny')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated name
    await expect(page.locator('text=Johnny Doe')).toBeVisible()
    
    // 6. Upgrade user to premium
    await page.click('button:has-text("Upgrade to Premium")')
    await page.click('button:has-text("Confirm Upgrade")')
    await waitForPageLoad(page)
    
    // Should see premium status
    await expect(page.locator('text=Premium User')).toBeVisible()
  })

  test('Admin Benefit Verification Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to benefit verifications
    await page.goto('/admin/verifications')
    await waitForPageLoad(page)
    
    // Should see pending verifications
    await expect(page.locator('text=Pending Verifications')).toBeVisible()
    
    // 3. Review verification request
    const verificationCard = page.locator('.verification-card').first()
    await expect(verificationCard.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(verificationCard.locator('text=E2E Industries')).toBeVisible()
    
    // 4. Approve verification
    await verificationCard.locator('button:has-text("Approve")').click()
    await page.fill('textarea[name="adminNotes"]', 'Verified through company documentation')
    await page.click('button:has-text("Confirm Approval")')
    await waitForPageLoad(page)
    
    // Should see success message
    await expect(page.locator('text=Verification approved')).toBeVisible()
    
    // 5. Check approved verifications
    await page.click('text=Approved')
    await waitForPageLoad(page)
    
    // Should see the approved verification
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=Approved by Admin User')).toBeVisible()
  })

  test('Admin Analytics and Reporting Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to analytics dashboard
    await page.goto('/admin/analytics')
    await waitForPageLoad(page)
    
    // Should see analytics overview
    await expect(page.locator('text=System Analytics')).toBeVisible()
    await expect(page.locator('text=Total Users')).toBeVisible()
    await expect(page.locator('text=Total Companies')).toBeVisible()
    await expect(page.locator('text=Total Benefits')).toBeVisible()
    
    // 3. View detailed reports
    await page.click('text=User Activity Report')
    await waitForPageLoad(page)
    
    // Should see user activity data
    await expect(page.locator('text=Daily Active Users')).toBeVisible()
    await expect(page.locator('text=Sign-ups This Month')).toBeVisible()
    
    // 4. Export data
    await page.click('button:has-text("Export Data")')
    await page.selectOption('select[name="exportType"]', 'csv')
    await page.selectOption('select[name="dateRange"]', 'last_30_days')
    await page.click('button:has-text("Generate Export")')
    await waitForPageLoad(page)
    
    // Should see export confirmation
    await expect(page.locator('text=Export generated successfully')).toBeVisible()
    
    // 5. View company analytics
    await page.click('text=Company Analytics')
    await waitForPageLoad(page)
    
    // Should see company-specific data
    await expect(page.locator('text=Most Popular Benefits')).toBeVisible()
    await expect(page.locator('text=Company Growth')).toBeVisible()
  })

  test('Super Admin Workflow', async ({ page }) => {
    // 1. Sign in as super admin
    await signInAdmin(page, '<EMAIL>')
    
    // 2. Navigate to super admin panel
    await page.goto('/admin/system')
    await waitForPageLoad(page)
    
    // Should see system management options
    await expect(page.locator('text=System Management')).toBeVisible()
    await expect(page.locator('text=Reset Analytics')).toBeVisible()
    await expect(page.locator('text=Manage Admins')).toBeVisible()
    
    // 3. Reset analytics data
    await page.click('button:has-text("Reset Analytics")')
    await page.selectOption('select[name="resetType"]', 'user_analytics')
    await page.fill('input[name="confirmationText"]', 'RESET')
    await page.click('button:has-text("Confirm Reset")')
    await waitForPageLoad(page)
    
    // Should see reset confirmation
    await expect(page.locator('text=Analytics data reset successfully')).toBeVisible()
    
    // 4. Manage admin users
    await page.click('text=Manage Admins')
    await waitForPageLoad(page)
    
    // Should see admin users list
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
    
    // 5. Create new admin
    await page.click('button:has-text("Add Admin")')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="firstName"]', 'New')
    await page.fill('input[name="lastName"]', 'Admin')
    await page.selectOption('select[name="role"]', 'admin')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see new admin in list
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('Admin Error Handling and Security', async ({ page }) => {
    // 1. Try to access admin panel without authentication
    await page.goto('/admin')
    
    // Should redirect to sign-in
    await expect(page).toHaveURL(/\/sign-in/)

    // 2. Sign in as regular user
    await page.fill('input[type="email"]', 'user1@techcorp.e2e')
    await page.click('button[type="submit"]')
    await page.goto('/auth/magic-link?token=mock-token&email=user1@techcorp.e2e')
    
    // 3. Try to access admin panel as regular user
    await page.goto('/admin')
    
    // Should see access denied
    await expect(page.locator('text=Access Denied')).toBeVisible()
    
    // 4. Sign in as admin
    await signInAdmin(page)
    
    // 5. Try to perform unauthorized action
    await page.goto('/admin/system') // Super admin only
    
    // Should see insufficient permissions
    await expect(page.locator('text=Insufficient permissions')).toBeVisible()
    
    // 6. Test form validation
    await page.goto('/admin/companies')
    await page.click('button:has-text("Add Company")')
    await page.click('button[type="submit"]') // Submit without filling required fields
    
    // Should see validation errors
    await expect(page.locator('text=Company name is required')).toBeVisible()
    await expect(page.locator('text=Domain is required')).toBeVisible()
  })

  test('Admin Bulk Operations Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to company management
    await page.goto('/admin/companies')
    await waitForPageLoad(page)
    
    // 3. Select multiple companies
    await page.check('input[type="checkbox"][value="e2e-company-1"]')
    await page.check('input[type="checkbox"][value="e2e-company-2"]')
    
    // Should see bulk actions toolbar
    await expect(page.locator('text=2 companies selected')).toBeVisible()
    
    // 4. Perform bulk verification
    await page.click('button:has-text("Bulk Verify")')
    await page.click('button:has-text("Confirm Bulk Verification")')
    await waitForPageLoad(page)
    
    // Should see success message
    await expect(page.locator('text=Companies verified successfully')).toBeVisible()
    
    // 5. Test bulk export
    await page.check('input[type="checkbox"][value="e2e-company-3"]')
    await page.click('button:has-text("Export Selected")')
    await page.selectOption('select[name="exportFormat"]', 'json')
    await page.click('button:has-text("Generate Export")')
    await waitForPageLoad(page)
    
    // Should see export confirmation
    await expect(page.locator('text=Export generated for 3 companies')).toBeVisible()
  })
})
