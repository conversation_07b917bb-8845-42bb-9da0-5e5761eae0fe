-- BenefitLens Database Seed Data
-- This file contains sample data for development and testing
-- Generated from actual database on 2025-08-02

-- Note: benefit_categories are inserted automatically via schema.sql

-- Insert sample benefits with proper category_id references
INSERT INTO benefits (name, category, icon, description, category_id) VALUES
-- Health benefits
('Gym Membership', 'wellness', '💪', NULL, (SELECT id FROM benefit_categories WHERE name = 'wellness')),
('Dental Insurance Plus (Zahnzusatzversicherung)', 'health', '🦷', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Company Health Check-ups (Betriebsärztliche Untersuchungen)', 'health', '🩺', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Mental Health Support (Psychologische Betreuung)', 'health', '🧠', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Occupational Health Services (Arbeitsmedizin)', 'health', '⚕️', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),

-- Time off benefits
('Unlimited PTO', 'time_off', '🏖️', NULL, (SELECT id FROM benefit_categories WHERE name = 'time_off')),

-- Financial benefits
('Stock Options', 'financial', '📈', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Retirement Plan', 'financial', '💰', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Holiday Bonus (Urlaubsgeld)', 'financial', '🏖️', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Company Pension Scheme (Betriebliche Altersvorsorge)', 'financial', '💰', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Profit Sharing (Gewinnbeteiligung)', 'financial', '📈', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Employee Stock Purchase Plan (Mitarbeiterbeteiligung)', 'financial', '📊', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),

-- Development benefits
('Learning Budget', 'development', '📚', NULL, (SELECT id FROM benefit_categories WHERE name = 'development')),

-- Other benefits
('Free Lunch', 'other', '🍽️', NULL, (SELECT id FROM benefit_categories WHERE name = 'other')),
('Pet-Friendly Office', 'other', '🐕', NULL, (SELECT id FROM benefit_categories WHERE name = 'other'));

-- Insert sample companies (based on actual data)
INSERT INTO companies (name, location, size, industry, description, domain, career_url) VALUES
('SAP', 'Walldorf, Germany', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', 'https://jobs.sap.com'),
('Deutsche Bank', 'Frankfurt, Germany', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', 'https://careers.db.com'),
('Accenture', 'Dublin, Ireland', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', 'https://www.accenture.com/careers'),
('PwC', 'London, UK', 'large', 'Consulting', 'One of the Big Four accounting firms providing audit, tax and consulting services', 'pwc.com', 'https://www.pwc.com/careers'),
('Commerzbank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Major German commercial bank', 'commerzbank.de', 'https://karriere.commerzbank.de'),
('DZ Bank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Central institution for cooperative banks in Germany', 'dzbank.de', 'https://karriere.dzbank.de'),
('Lufthansa', 'Cologne, Germany', 'enterprise', 'Aviation', 'German airline and aviation group', 'lufthansa.com', 'https://careers.lufthansagroup.com'),
('Siemens', 'Munich, Germany', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', 'https://jobs.siemens.com');

-- Insert sample company benefits (linking companies with benefits)
-- This creates realistic benefit associations for the sample companies
INSERT INTO company_benefits (company_id, benefit_id, is_verified)
SELECT
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE
    (c.name = 'SAP' AND b.name IN ('Gym Membership', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Retirement Plan', 'Stock Options', 'Gym Membership', 'Free Lunch', 'Company Pension Scheme (Betriebliche Altersvorsorge)')) OR
    (c.name = 'Accenture' AND b.name IN ('Learning Budget', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership', 'Profit Sharing (Gewinnbeteiligung)')) OR
    (c.name = 'PwC' AND b.name IN ('Learning Budget', 'Gym Membership', 'Company Health Check-ups (Betriebsärztliche Untersuchungen)', 'Holiday Bonus (Urlaubsgeld)')) OR
    (c.name = 'Commerzbank' AND b.name IN ('Retirement Plan', 'Stock Options', 'Gym Membership', 'Company Pension Scheme (Betriebliche Altersvorsorge)')) OR
    (c.name = 'DZ Bank' AND b.name IN ('Retirement Plan', 'Learning Budget', 'Company Pension Scheme (Betriebliche Altersvorsorge)')) OR
    (c.name = 'Lufthansa' AND b.name IN ('Retirement Plan', 'Free Lunch', 'Unlimited PTO', 'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)')) OR
    (c.name = 'Siemens' AND b.name IN ('Learning Budget', 'Stock Options', 'Gym Membership', 'Mental Health Support (Psychologische Betreuung)', 'Occupational Health Services (Arbeitsmedizin)'));

-- Insert sample admin user (for testing purposes)
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status) VALUES
('<EMAIL>', 'Admin', 'User', true, 'admin', 'free');

-- Insert sample regular users
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status, company_id) VALUES
('<EMAIL>', 'John', 'Doe', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'SAP')),
('<EMAIL>', 'Jane', 'Smith', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Deutsche Bank')),
('<EMAIL>', 'Mike', 'Johnson', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Accenture'));
