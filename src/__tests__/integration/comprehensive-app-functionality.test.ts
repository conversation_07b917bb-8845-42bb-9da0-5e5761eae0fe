/**
 * Comprehensive App Functionality Integration Tests
 * Tests ALL app functionality with real PostgreSQL database
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import {
  setupTestDatabase,
  cleanupTestDatabase,
  createTestUserSession,
  createAuthHeaders,
  createAuthHeadersWithCSRF
} from './setup'

const BASE_URL = process.env.TEST_SERVER_URL || 'http://localhost:3000'

// Test data IDs that match the setup.ts seeding
const TEST_COMPANY_IDS = {
  'testcorp.test': 'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'testind.test': 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'startup.test': 'ffffffff-ffff-ffff-ffff-ffffffffffff',
  'consulting.test': '10101010-1010-1010-1010-101010101010',
  'finance.test': '20202020-2020-2020-2020-202020202020'
}

const TEST_BENEFIT_IDS = {
  'Test Health Insurance': '55555555-5555-5555-5555-555555555555',
  'Test Remote Work': '66666666-6666-6666-6666-666666666666',
  'Test Dental Coverage': '77777777-7777-7777-7777-777777777777',
  'Test Stock Options': '88888888-8888-8888-8888-888888888888',
  'Test Gym Membership': '99999999-9999-9999-9999-999999999999',
  'Test Mental Health Support': 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'Test Flexible Hours': 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'Test Pension Plan': 'cccccccc-cccc-cccc-cccc-cccccccccccc'
}

describe('Comprehensive App Functionality Integration Tests', () => {
  beforeAll(async () => {
    console.log('🧪 Starting comprehensive integration tests with real PostgreSQL database')
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    // Clean and reseed before each test for isolation
    await setupTestDatabase()
  })

  describe('User Authentication & Profile Management', () => {
    it('should handle user profile retrieval', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)
      
      const response = await fetch(`${BASE_URL}/api/auth/me`, { headers })
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('id')
      expect(data.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i) // UUID format
      expect(data).toHaveProperty('email', '<EMAIL>')
      expect(data).toHaveProperty('first_name', 'John')
      expect(data).toHaveProperty('last_name', 'Doe')
      expect(data).toHaveProperty('role', 'user')
      expect(data).toHaveProperty('company_domain', 'testcorp.test')
      expect(data).toHaveProperty('is_premium', false)
    })

    it('should handle user email change', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = await createAuthHeadersWithCSRF(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/profile`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('user')
      expect(data.user).toHaveProperty('email', '<EMAIL>')
    })

    it('should reject unauthorized profile access', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/me`)
      
      expect(response.status).toBe(401)
    })

    it('should handle invalid session tokens', async () => {
      const headers = createAuthHeaders('invalid-session-token')
      
      const response = await fetch(`${BASE_URL}/api/auth/me`, { headers })
      
      expect(response.status).toBe(401)
    })
  })

  describe('Company Discovery & Management', () => {
    it('should display all companies on the start page without filters', async () => {
      // Test that all companies are returned when no filters are applied (default state)
      const response = await fetch(`${BASE_URL}/api/companies`)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('companies')
      expect(data).toHaveProperty('total')
      expect(Array.isArray(data.companies)).toBe(true)
      expect(data.companies.length).toBeGreaterThan(100) // Should show most/all companies
      expect(data.total).toBeGreaterThan(100)
      expect(data.companies.length).toBe(data.total) // Should show all companies on first page

      // Verify companies have required fields for display
      const firstCompany = data.companies[0]
      expect(firstCompany).toHaveProperty('id')
      expect(firstCompany).toHaveProperty('name')
      expect(firstCompany).toHaveProperty('industry')
      expect(firstCompany).toHaveProperty('size')
      expect(firstCompany).toHaveProperty('company_benefits')
      expect(firstCompany).toHaveProperty('locations')
      expect(Array.isArray(firstCompany.company_benefits)).toBe(true)
      expect(Array.isArray(firstCompany.locations)).toBe(true)
    })

    it('should return companies with pagination and filters', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?limit=3&page=1`)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('companies')
      expect(data).toHaveProperty('total')
      expect(data).toHaveProperty('page', 1)
      expect(data).toHaveProperty('limit', 3)
      expect(Array.isArray(data.companies)).toBe(true)
      expect(data.companies.length).toBeLessThanOrEqual(3)
      expect(data.total).toBeGreaterThan(0)
    })

    it('should filter companies by location', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?location=Berlin`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.companies.length).toBeGreaterThan(0)
      
      // Verify companies have Berlin location
      for (const company of data.companies) {
        const companyResponse = await fetch(`${BASE_URL}/api/companies/${company.id}`)
        const companyData = await companyResponse.json()
        expect(companyData.locations.some((loc: any) => loc.city === 'Berlin')).toBe(true)
      }
    })

    it('should filter companies by benefits', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?benefits=Test Health Insurance`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.companies.length).toBeGreaterThan(0)
      
      // Verify companies have the requested benefit
      for (const company of data.companies) {
        const companyResponse = await fetch(`${BASE_URL}/api/companies/${company.id}`)
        const companyData = await companyResponse.json()
        expect(companyData.benefits.some((b: any) => b.name === 'Test Health Insurance')).toBe(true)
      }
    })

    it('should filter companies by industry', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?industry=Technology`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.companies.length).toBeGreaterThan(0)
      expect(data.companies.every((c: any) => c.industry === 'Technology')).toBe(true)
    })

    it('should filter companies by size', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?size=medium`)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data.companies.length).toBeGreaterThan(0)
      expect(data.companies.every((c: any) => c.size === 'medium')).toBe(true)
    })

    it('should return detailed company information', async () => {
      const testCorpId = TEST_COMPANY_IDS['testcorp.test']
      const response = await fetch(`${BASE_URL}/api/companies/${testCorpId}`)

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('id', testCorpId)
      expect(data).toHaveProperty('name', 'Test Corp')
      expect(data).toHaveProperty('domain', 'testcorp.test')
      expect(data).toHaveProperty('industry', 'Technology')
      expect(data).toHaveProperty('size', 'medium')

      expect(data).toHaveProperty('benefits')
      expect(data).toHaveProperty('locations')
      expect(Array.isArray(data.benefits)).toBe(true)
      expect(Array.isArray(data.locations)).toBe(true)
      expect(data.benefits.length).toBeGreaterThan(0)
      expect(data.locations.length).toBeGreaterThan(0)
    })

    it('should handle non-existent company', async () => {
      const response = await fetch(`${BASE_URL}/api/companies/non-existent`)
      
      expect(response.status).toBe(404)
    })

    it('should search companies by name', async () => {
      const response = await fetch(`${BASE_URL}/api/companies?search=Test Corp`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.companies.length).toBeGreaterThan(0)
      expect(data.companies.some((c: any) => c.name.includes('Test Corp'))).toBe(true)
    })
  })

  describe('Benefits Management', () => {
    it('should return benefits in correct format', async () => {
      const response = await fetch(`${BASE_URL}/api/benefits`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('benefits')
      expect(Array.isArray(data.benefits)).toBe(true)
      expect(data.benefits.length).toBeGreaterThan(0)
      
      // Verify benefit structure
      const benefit = data.benefits[0]
      expect(benefit).toHaveProperty('id')
      expect(benefit).toHaveProperty('name')
      expect(benefit).toHaveProperty('description')
      expect(benefit).toHaveProperty('category_id')
      expect(benefit).toHaveProperty('icon')
    })

    it('should filter benefits by category', async () => {
      const response = await fetch(`${BASE_URL}/api/benefits?category=test_health`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.benefits.every((b: any) => b.category_name === 'test_health')).toBe(true)
    })

    it('should search benefits by name', async () => {
      const response = await fetch(`${BASE_URL}/api/benefits?search=health`)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.benefits.some((b: any) => b.name.toLowerCase().includes('health'))).toBe(true)
    })
  })

  describe('User Benefit Management', () => {
    it('should allow authenticated users to add benefits to their company', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = await createAuthHeadersWithCSRF(sessionToken)

      const testCorpId = TEST_COMPANY_IDS['testcorp.test']
      const dentalId = TEST_BENEFIT_IDS['Test Dental Coverage']
      const gymId = TEST_BENEFIT_IDS['Test Gym Membership']

      const response = await fetch(`${BASE_URL}/api/companies/${testCorpId}/benefits`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          benefitIds: [dentalId, gymId]
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('added')
      expect(Array.isArray(data.added)).toBe(true)
      expect(data.added.length).toBe(2)
    })

    it('should reject unauthorized benefit additions', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>') // Different company
      const headers = createAuthHeaders(sessionToken)

      const testCorpId = TEST_COMPANY_IDS['testcorp.test']
      const dentalId = TEST_BENEFIT_IDS['Test Dental Coverage']

      const response = await fetch(`${BASE_URL}/api/companies/${testCorpId}/benefits`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          benefitIds: [dentalId]
        })
      })
      
      expect(response.status).toBe(403)
    })

    it('should allow users to remove benefits from their company', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const testCorpId = TEST_COMPANY_IDS['testcorp.test']
      const healthId = TEST_BENEFIT_IDS['Test Health Insurance']

      const response = await fetch(`${BASE_URL}/api/companies/${testCorpId}/benefits/${healthId}`, {
        method: 'DELETE',
        headers
      })
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })
  })

  describe('User Benefit Rankings', () => {
    it('should allow users to create benefit rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          rankings: [
            { benefitId: TEST_BENEFIT_IDS['Test Health Insurance'], ranking: 1 },
            { benefitId: TEST_BENEFIT_IDS['Test Remote Work'], ranking: 2 },
            { benefitId: TEST_BENEFIT_IDS['Test Dental Coverage'], ranking: 3 }
          ]
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('rankings')
      expect(Array.isArray(data.rankings)).toBe(true)
      expect(data.rankings.length).toBe(3)
    })

    it('should allow users to update benefit rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          rankings: [
            { benefitId: TEST_BENEFIT_IDS['Test Remote Work'], ranking: 1 }, // Changed order
            { benefitId: TEST_BENEFIT_IDS['Test Health Insurance'], ranking: 2 },
            { benefitId: TEST_BENEFIT_IDS['Test Stock Options'], ranking: 3 }
          ]
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow users to add benefits to existing rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const gymId = TEST_BENEFIT_IDS['Test Gym Membership']
      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings/${gymId}`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ranking: 4
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow users to remove benefits from rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const stockId = TEST_BENEFIT_IDS['Test Stock Options']
      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings/${stockId}`, {
        method: 'DELETE',
        headers
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow users to reset all benefit rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings`, {
        method: 'DELETE',
        headers
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('deletedCount')
      expect(typeof data.deletedCount).toBe('number')
    })

    it('should retrieve user benefit rankings', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('rankings')
      expect(Array.isArray(data.rankings)).toBe(true)
      expect(data.rankings.length).toBeGreaterThan(0)

      // Verify ranking structure
      const ranking = data.rankings[0]
      expect(ranking).toHaveProperty('benefit_id')
      expect(ranking).toHaveProperty('ranking')
      expect(ranking).toHaveProperty('benefit_name')
      expect(typeof ranking.ranking).toBe('number')
    })
  })

  describe('Missing Company Reports', () => {
    it('should allow users to report missing companies', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/missing-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyName: 'New Missing Company',
          companyDomain: 'newmissing.test',
          industry: 'Technology',
          description: 'This company is missing from the database'
        })
      })

      expect(response.status).toBe(201)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('reportId')
      expect(typeof data.reportId).toBe('string')
    })

    it('should prevent duplicate missing company reports', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      // First report
      await fetch(`${BASE_URL}/api/missing-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyName: 'Duplicate Company',
          companyDomain: 'duplicate.test',
          industry: 'Technology'
        })
      })

      // Second report (should be rejected)
      const response = await fetch(`${BASE_URL}/api/missing-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyName: 'Duplicate Company',
          companyDomain: 'duplicate.test',
          industry: 'Technology'
        })
      })

      expect(response.status).toBe(409) // Conflict
    })

    it('should allow users to view their missing company reports', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/missing-company-reports`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('reports')
      expect(Array.isArray(data.reports)).toBe(true)
    })
  })

  describe('Saved Companies', () => {
    it('should allow users to save companies', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/saved-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testcorp.test']
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow users to unsave companies', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const testIndId = TEST_COMPANY_IDS['testind.test']
      const response = await fetch(`${BASE_URL}/api/user/saved-companies/${testIndId}`, {
        method: 'DELETE',
        headers
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should retrieve user saved companies', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/saved-companies`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('savedCompanies')
      expect(Array.isArray(data.savedCompanies)).toBe(true)
    })

    it('should prevent duplicate saved companies', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      // Try to save already saved company (<EMAIL> has saved testind.test company)
      const response = await fetch(`${BASE_URL}/api/user/saved-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testind.test'] // Already saved in test data
        })
      })

      expect(response.status).toBe(409) // Conflict
    })
  })

  describe('Analytics & Insights', () => {
    it('should allow premium users to access analytics', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>') // Premium user
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/analytics/user-insights`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('insights')
      expect(data).toHaveProperty('benefitRankings')
      expect(data).toHaveProperty('companyMatches')
      expect(Array.isArray(data.insights)).toBe(true)
    })

    it('should restrict non-premium users to preview analytics', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>') // Non-premium user
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/analytics/user-insights`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('preview', true)
      expect(data).toHaveProperty('upgradeRequired', true)
      expect(data.insights).toBeDefined()
      // Preview should have limited data
      if (Array.isArray(data.insights)) {
        expect(data.insights.length).toBeLessThanOrEqual(3)
      }
    })

    it('should track analytics events', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = await createAuthHeadersWithCSRF(sessionToken)

      const response = await fetch(`${BASE_URL}/api/analytics/track`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          eventType: 'company_view',
          eventData: {
            companyId: TEST_COMPANY_IDS['testcorp.test'],
            source: 'search_results'
          }
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should handle anonymous analytics tracking', async () => {
      const response = await fetch(`${BASE_URL}/api/analytics/track`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType: 'page_view',
          eventData: {
            page: '/benefits',
            anonymous: true
          }
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should provide benefit ranking analytics for premium users', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>') // Premium user
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/analytics/benefit-rankings`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('userRankings')
      expect(data).toHaveProperty('popularBenefits')
      expect(data).toHaveProperty('industryComparisons')
      expect(Array.isArray(data.userRankings)).toBe(true)
      expect(Array.isArray(data.popularBenefits)).toBe(true)
    })
  })

  describe('Benefit Verification', () => {
    it('should allow users to submit benefit verifications', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = await createAuthHeadersWithCSRF(sessionToken)

      const response = await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testcorp.test'],
          benefitId: TEST_BENEFIT_IDS['Test Mental Health Support'],
          evidence: 'I work at this company and can confirm this benefit exists',
          contactInfo: '<EMAIL>'
        })
      })

      expect(response.status).toBe(201)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('verificationId')
    })

    it('should prevent duplicate benefit verifications', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      // First, submit a verification
      const firstResponse = await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['startup.test'],
          benefitId: TEST_BENEFIT_IDS['Test Health Insurance'],
          evidence: 'First verification'
        })
      })

      expect(firstResponse.status).toBe(201)

      // Now try to submit duplicate verification
      const response = await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['startup.test'],
          benefitId: TEST_BENEFIT_IDS['Test Health Insurance'],
          evidence: 'Duplicate verification attempt'
        })
      })

      expect(response.status).toBe(409) // Conflict
    })

    it('should allow users to view their benefit verification submissions', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      // First, create a verification
      await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['startup.test'],
          benefitId: TEST_BENEFIT_IDS['Test Health Insurance'],
          evidence: 'Test verification for viewing'
        })
      })

      const response = await fetch(`${BASE_URL}/api/user/benefit-verifications`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('verifications')
      expect(Array.isArray(data.verifications)).toBe(true)
      expect(data.verifications.length).toBeGreaterThan(0)

      // Verify verification structure
      const verification = data.verifications[0]
      expect(verification).toHaveProperty('id')
      expect(verification).toHaveProperty('company_name')
      expect(verification).toHaveProperty('benefit_name')
      expect(verification).toHaveProperty('status')
      expect(['confirmed', 'disputed']).toContain(verification.status)
    })
  })

  describe('Admin Functionality', () => {
    it('should allow admins to view pending benefit verifications', async () => {
      // First create a verification as a regular user
      const userSessionToken = await createTestUserSession('<EMAIL>')
      const userHeaders = createAuthHeaders(userSessionToken)

      await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers: userHeaders,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testcorp.test'],
          benefitId: TEST_BENEFIT_IDS['Test Health Insurance'],
          evidence: 'Test verification for admin review'
        })
      })

      // Now check as admin
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/benefit-verifications`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('verifications')
      expect(Array.isArray(data.verifications)).toBe(true)
    })

    it('should allow admins to approve benefit verifications', async () => {
      // First create a verification as a regular user
      const userSessionToken = await createTestUserSession('<EMAIL>')
      const userHeaders = createAuthHeaders(userSessionToken)

      const createResponse = await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers: userHeaders,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testind.test'],
          benefitId: TEST_BENEFIT_IDS['Test Health Insurance'],
          evidence: 'Test verification for admin approval'
        })
      })

      const createData = await createResponse.json()
      const verificationId = createData.verificationId

      // Now approve as admin
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/benefit-verifications/${verificationId}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'approved',
          adminNotes: 'Verification approved after review'
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow admins to reject benefit verifications', async () => {
      // First create a verification as a regular user
      const userSessionToken = await createTestUserSession('<EMAIL>')
      const userHeaders = createAuthHeaders(userSessionToken)

      const createResponse = await fetch(`${BASE_URL}/api/benefit-verifications`, {
        method: 'POST',
        headers: userHeaders,
        body: JSON.stringify({
          companyId: TEST_COMPANY_IDS['testcorp.test'],
          benefitId: TEST_BENEFIT_IDS['Test Remote Work'],
          evidence: 'Test verification for admin rejection'
        })
      })

      const createData = await createResponse.json()
      const verificationId = createData.verificationId

      // Now reject as admin
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/benefit-verifications/${verificationId}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'rejected',
          adminNotes: 'Insufficient evidence provided'
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow admins to view missing company reports', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/missing-companies`, { headers })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('reports')
      expect(Array.isArray(data.reports)).toBe(true)
    })

    it('should allow admins to approve missing company reports', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/missing-companies/11111111-1111-1111-1111-111111111111`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'approved',
          adminNotes: 'Company added to database'
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
    })

    it('should allow admins to reset user analytics', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/analytics/reset`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          userId: 'test-user-1',
          resetType: 'benefit_rankings'
        })
      })

      expect(response.status).toBe(200)

      const data = await response.json()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('resetCount')
    })

    it('should restrict admin endpoints to admin users only', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>') // Regular user
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/admin/benefit-verifications`, { headers })

      expect(response.status).toBe(403) // Forbidden
    })
  })

  describe('Error Handling & Edge Cases', () => {
    it('should handle malformed JSON requests', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = await createAuthHeadersWithCSRF(sessionToken)

      const response = await fetch(`${BASE_URL}/api/user/benefit-rankings`, {
        method: 'POST',
        headers,
        body: 'invalid json'
      })

      expect(response.status).toBe(400)
    })

    it('should handle missing required fields', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      const response = await fetch(`${BASE_URL}/api/missing-companies`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          // Missing required companyName field
          companyDomain: 'incomplete.test'
        })
      })

      expect(response.status).toBe(400)
    })

    it('should handle database connection errors gracefully', async () => {
      // This test would require temporarily breaking the database connection
      // For now, we'll test that the API returns appropriate error responses
      const response = await fetch(`${BASE_URL}/api/companies/invalid-id-format`)

      expect([400, 404, 500]).toContain(response.status)
    })

    it('should handle rate limiting', async () => {
      const sessionToken = await createTestUserSession('<EMAIL>')
      const headers = createAuthHeaders(sessionToken)

      // Make multiple rapid requests to trigger rate limiting
      const promises = Array.from({ length: 20 }, () =>
        fetch(`${BASE_URL}/api/analytics/track`, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            eventType: 'test_event',
            eventData: { test: true }
          })
        })
      )

      const responses = await Promise.all(promises)

      // At least some requests should succeed
      expect(responses.some(r => r.status === 200)).toBe(true)

      // Some might be rate limited (429) or succeed (200)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status)
      })
    })
  })
})
