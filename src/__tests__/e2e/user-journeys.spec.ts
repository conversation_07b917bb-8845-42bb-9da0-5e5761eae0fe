/**
 * Critical User Journey E2E Tests
 * Tests complete user workflows from start to finish
 */

import { test, expect, Page } from '@playwright/test'

// Helper functions
async function signInUser(page: Page, email: string) {
  await page.goto('/sign-in')
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // In a real app, this would involve email verification
  // For E2E tests, we'll mock the magic link process
  await page.goto('/auth/magic-link?token=mock-token&email=' + encodeURIComponent(email))

  // Wait for redirect to dashboard or home
  await page.waitForURL(/\/(dashboard|$)/)
}

async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle')
  await page.waitForTimeout(1000) // Additional wait for dynamic content
}

test.describe('Critical User Journeys', () => {
  test.beforeEach(async ({ page }) => {
    // Ensure clean state
    await page.context().clearCookies()
  })

  test('Complete User Registration and Company Discovery Journey', async ({ page }) => {
    // 1. User visits homepage
    await page.goto('/')
    await waitForPageLoad(page)
    
    expect(await page.title()).toContain('BenefitLens')
    
    // 2. User searches for companies
    await page.fill('input[placeholder*="Search companies"]', 'E2E Tech')
    await page.press('input[placeholder*="Search companies"]', 'Enter')
    await waitForPageLoad(page)
    
    // Should see E2E Tech Corp in results
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    
    // 3. User clicks on company to view details
    await page.click('text=E2E Tech Corp')
    await waitForPageLoad(page)
    
    // Should see company profile page
    await expect(page.locator('h1:has-text("E2E Tech Corp")')).toBeVisible()
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).toBeVisible()
    
    // 4. User decides to sign up
    await page.click('text=Sign In')
    await page.click('text=Sign Up')
    
    // 5. User fills registration form
    await page.fill('input[name="email"]', 'newuser@techcorp.e2e')
    await page.fill('input[name="firstName"]', 'New')
    await page.fill('input[name="lastName"]', 'User')
    await page.click('button[type="submit"]')
    
    // 6. User completes verification (mocked)
    await page.goto('/auth/magic-link?token=mock-token&email=newuser@techcorp.e2e')
    await waitForPageLoad(page)
    
    // 7. User should be redirected to dashboard
    await expect(page.locator('text=Welcome, New')).toBeVisible()
    await expect(page.locator('text=techcorp.e2e')).toBeVisible()
  })

  test('User Benefit Management Journey', async ({ page }) => {
    // 1. Sign in as existing user
    await signInUser(page, 'user1@techcorp.e2e')
    
    // 2. Navigate to dashboard
    await page.goto('/dashboard')
    await waitForPageLoad(page)
    
    // Should see user's company benefits
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).toBeVisible()
    
    // 3. User wants to add more benefits
    await page.click('text=Add Benefits')
    await waitForPageLoad(page)
    
    // Should see available benefits modal
    await expect(page.locator('text=Add Benefits to E2E Tech Corp')).toBeVisible()
    
    // Should see benefits not already added
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=E2E Gym Membership')).toBeVisible()
    
    // 4. User selects benefits to add
    await page.check('input[type="checkbox"][value="e2e-benefit-3"]') // Dental
    await page.check('input[type="checkbox"][value="e2e-benefit-5"]') // Gym
    
    // 5. User submits benefit additions
    await page.click('button:has-text("Add Selected Benefits")')
    await waitForPageLoad(page)
    
    // 6. Should see success message and updated benefits list
    await expect(page.locator('text=Benefits added successfully')).toBeVisible()
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=E2E Gym Membership')).toBeVisible()
  })

  test('User Benefit Ranking Journey', async ({ page }) => {
    // 1. Sign in as premium user
    await signInUser(page, 'user2@industries.e2e')
    
    // 2. Navigate to benefit ranking
    await page.goto('/benefit-ranking')
    await waitForPageLoad(page)
    
    // Should see benefit ranking interface
    await expect(page.locator('text=Rank Your Benefits')).toBeVisible()
    
    // 3. User drags benefits to reorder
    const healthBenefit = page.locator('text=E2E Health Insurance').first()
    const dentalBenefit = page.locator('text=E2E Dental Coverage').first()
    
    // Drag health insurance below dental coverage
    await healthBenefit.dragTo(dentalBenefit)
    await waitForPageLoad(page)
    
    // 4. User saves ranking
    await page.click('button:has-text("Save Ranking")')
    await waitForPageLoad(page)
    
    // Should see success message
    await expect(page.locator('text=Ranking saved successfully')).toBeVisible()
    
    // 5. Navigate to analytics to see ranking insights
    await page.goto('/analytics')
    await waitForPageLoad(page)
    
    // Premium user should see full analytics
    await expect(page.locator('text=Your Benefit Insights')).toBeVisible()
    await expect(page.locator('text=Top Ranked Benefits')).toBeVisible()
  })

  test('Company Search and Filter Journey', async ({ page }) => {
    // 1. User visits homepage
    await page.goto('/')
    await waitForPageLoad(page)
    
    // 2. User searches by location
    await page.fill('input[placeholder*="location"]', 'Berlin')
    await page.press('input[placeholder*="location"]', 'Enter')
    await waitForPageLoad(page)
    
    // Should see companies in Berlin
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    
    // 3. User filters by benefits
    await page.click('text=Filter by Benefits')
    await page.check('input[type="checkbox"][value="E2E Health Insurance"]')
    await waitForPageLoad(page)
    
    // Should see only companies with health insurance
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    await expect(page.locator('text=E2E Industries')).toBeVisible()
    
    // 4. User filters by company size
    await page.selectOption('select[name="size"]', '100-500')
    await waitForPageLoad(page)
    
    // Should see only medium-sized companies
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    
    // 5. User saves a company
    await page.click('button[aria-label="Save E2E Tech Corp"]')
    
    // Should see save confirmation
    await expect(page.locator('text=Company saved')).toBeVisible()
  })

  test('Benefits Discovery Journey', async ({ page }) => {
    // 1. User visits benefits page
    await page.goto('/benefits')
    await waitForPageLoad(page)
    
    // Should see all benefits
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).toBeVisible()
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    
    // 2. User filters by category
    await page.click('text=Health')
    await waitForPageLoad(page)
    
    // Should see only health benefits
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).not.toBeVisible()
    
    // 3. User searches for specific benefit
    await page.fill('input[placeholder*="Search benefits"]', 'dental')
    await waitForPageLoad(page)
    
    // Should see only dental benefits
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=E2E Health Insurance')).not.toBeVisible()
    
    // 4. User clicks on benefit to filter companies
    await page.click('text=E2E Dental Coverage')
    
    // Should redirect to main page with benefit filter
    await expect(page).toHaveURL(/\?.*benefits=E2E%20Dental%20Coverage/)
    await waitForPageLoad(page)
    
    // Should see companies offering dental coverage
    await expect(page.locator('text=E2E Industries')).toBeVisible()
  })

  test('Mobile User Journey', async ({ page, isMobile }) => {
    test.skip(!isMobile, 'This test is only for mobile')
    
    // 1. User visits homepage on mobile
    await page.goto('/')
    await waitForPageLoad(page)
    
    // 2. User opens mobile menu
    await page.click('button[aria-label="Menu"]')
    
    // Should see mobile navigation
    await expect(page.locator('text=Companies')).toBeVisible()
    await expect(page.locator('text=Benefits')).toBeVisible()
    
    // 3. User navigates to companies
    await page.click('text=Companies')
    await waitForPageLoad(page)
    
    // Should see companies list optimized for mobile
    await expect(page.locator('text=E2E Tech Corp')).toBeVisible()
    
    // 4. User taps on company card
    await page.tap('text=E2E Tech Corp')
    await waitForPageLoad(page)
    
    // Should see mobile-optimized company profile
    await expect(page.locator('h1:has-text("E2E Tech Corp")')).toBeVisible()
    
    // 5. User scrolls to see benefits
    await page.locator('text=Benefits').scrollIntoViewIfNeeded()
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
  })

  test('Error Handling Journey', async ({ page }) => {
    // 1. User tries to access protected page without authentication
    await page.goto('/dashboard')
    
    // Should redirect to sign-in
    await expect(page).toHaveURL(/\/auth\/sign-in/)
    
    // 2. User enters invalid email
    await page.fill('input[type="email"]', 'invalid-email')
    await page.click('button[type="submit"]')
    
    // Should see validation error
    await expect(page.locator('text=Please enter a valid email')).toBeVisible()
    
    // 3. User tries to access non-existent company
    await page.goto('/companies/non-existent-company')
    
    // Should see 404 page
    await expect(page.locator('text=Company not found')).toBeVisible()
    
    // 4. User should be able to navigate back to homepage
    await page.click('text=Go Home')
    await expect(page).toHaveURL('/')
  })

  test('Performance and Loading Journey', async ({ page }) => {
    // 1. Measure homepage load time
    const startTime = Date.now()
    await page.goto('/')
    await waitForPageLoad(page)
    const loadTime = Date.now() - startTime
    
    // Should load within reasonable time (5 seconds)
    expect(loadTime).toBeLessThan(5000)
    
    // 2. Test search performance
    const searchStart = Date.now()
    await page.fill('input[placeholder*="Search companies"]', 'E2E')
    await page.press('input[placeholder*="Search companies"]', 'Enter')
    await waitForPageLoad(page)
    const searchTime = Date.now() - searchStart
    
    // Search should be fast (3 seconds)
    expect(searchTime).toBeLessThan(3000)
    
    // 3. Test navigation performance
    const navStart = Date.now()
    await page.click('text=Benefits')
    await waitForPageLoad(page)
    const navTime = Date.now() - navStart
    
    // Navigation should be fast (2 seconds)
    expect(navTime).toBeLessThan(2000)
  })
})
