# Page snapshot

```yaml
- generic [active] [ref=e1]:
  - generic [ref=e2]:
    - banner [ref=e3]:
      - generic [ref=e4]:
        - link "✓ BenefitLens" [ref=e5] [cursor=pointer]:
          - /url: /
          - generic [ref=e7] [cursor=pointer]: ✓
          - generic [ref=e8] [cursor=pointer]: BenefitLens
        - navigation [ref=e9]:
          - link "Companies" [ref=e10] [cursor=pointer]:
            - /url: /
          - link "Benefits" [ref=e11] [cursor=pointer]:
            - /url: /benefits
          - link "About" [ref=e12] [cursor=pointer]:
            - /url: /about
        - generic [ref=e13]:
          - link "Sign In" [ref=e14] [cursor=pointer]:
            - /url: /sign-in
            - button "Sign In" [ref=e15]
          - link "Join your company" [ref=e16] [cursor=pointer]:
            - /url: /sign-up
            - button "Join your company" [ref=e17]:
              - generic [ref=e18]: Join your company
    - main [ref=e19]:
      - generic [ref=e20]:
        - heading "Employee Benefits" [level=1] [ref=e21]
        - paragraph [ref=e22]: Explore the comprehensive list of benefits offered by companies on our platform.
      - generic [ref=e23]:
        - generic [ref=e24]:
          - generic [ref=e25]:
            - img [ref=e26]
            - heading "Filter by Category" [level=2] [ref=e28]
          - generic [ref=e29]:
            - button "All Benefits" [ref=e30]
            - button "Health & Medical" [ref=e31]
            - button "Time Off" [ref=e32]
            - button "Financial" [ref=e33]
            - button "Development" [ref=e34]
            - button "Fitness" [ref=e35]
            - button "Work-Life Balance" [ref=e36]
            - button "Other" [ref=e37]
        - generic [ref=e38]:
          - generic [ref=e39]:
            - heading "Health & Medical" [level=3] [ref=e40]
            - generic [ref=e41]:
              - button "🩺 Company Health Check-ups (Betriebsärztliche Untersuchungen) health" [ref=e42] [cursor=pointer]:
                - generic [ref=e43] [cursor=pointer]:
                  - generic [ref=e44] [cursor=pointer]:
                    - generic [ref=e45] [cursor=pointer]: 🩺
                    - generic [ref=e46] [cursor=pointer]:
                      - heading "Company Health Check-ups (Betriebsärztliche Untersuchungen)" [level=4] [ref=e47] [cursor=pointer]
                      - paragraph [ref=e48] [cursor=pointer]: health
                  - img [ref=e49] [cursor=pointer]
              - button "🦷 Dental Insurance Plus (Zahnzusatzversicherung) health" [ref=e51] [cursor=pointer]:
                - generic [ref=e52] [cursor=pointer]:
                  - generic [ref=e53] [cursor=pointer]:
                    - generic [ref=e54] [cursor=pointer]: 🦷
                    - generic [ref=e55] [cursor=pointer]:
                      - heading "Dental Insurance Plus (Zahnzusatzversicherung)" [level=4] [ref=e56] [cursor=pointer]
                      - paragraph [ref=e57] [cursor=pointer]: health
                  - img [ref=e58] [cursor=pointer]
              - button "🧠 Mental Health Support (Psychologische Betreuung) health" [ref=e60] [cursor=pointer]:
                - generic [ref=e61] [cursor=pointer]:
                  - generic [ref=e62] [cursor=pointer]:
                    - generic [ref=e63] [cursor=pointer]: 🧠
                    - generic [ref=e64] [cursor=pointer]:
                      - heading "Mental Health Support (Psychologische Betreuung)" [level=4] [ref=e65] [cursor=pointer]
                      - paragraph [ref=e66] [cursor=pointer]: health
                  - img [ref=e67] [cursor=pointer]
              - button "⚕️ Occupational Health Services (Arbeitsmedizin) health" [ref=e69] [cursor=pointer]:
                - generic [ref=e70] [cursor=pointer]:
                  - generic [ref=e71] [cursor=pointer]:
                    - generic [ref=e72] [cursor=pointer]: ⚕️
                    - generic [ref=e73] [cursor=pointer]:
                      - heading "Occupational Health Services (Arbeitsmedizin)" [level=4] [ref=e74] [cursor=pointer]
                      - paragraph [ref=e75] [cursor=pointer]: health
                  - img [ref=e76] [cursor=pointer]
          - generic [ref=e78]:
            - heading "Time Off" [level=3] [ref=e79]
            - generic [ref=e80]:
              - button "🏖️ 30 Days Vacation time off" [ref=e81] [cursor=pointer]:
                - generic [ref=e82] [cursor=pointer]:
                  - generic [ref=e83] [cursor=pointer]:
                    - generic [ref=e84] [cursor=pointer]: 🏖️
                    - generic [ref=e85] [cursor=pointer]:
                      - heading "30 Days Vacation" [level=4] [ref=e86] [cursor=pointer]
                      - paragraph [ref=e87] [cursor=pointer]: time off
                  - img [ref=e88] [cursor=pointer]
              - button "🌴 Extended Vacation Days (Zusätzliche Urlaubstage) time off" [ref=e90] [cursor=pointer]:
                - generic [ref=e91] [cursor=pointer]:
                  - generic [ref=e92] [cursor=pointer]:
                    - generic [ref=e93] [cursor=pointer]: 🌴
                    - generic [ref=e94] [cursor=pointer]:
                      - heading "Extended Vacation Days (Zusätzliche Urlaubstage)" [level=4] [ref=e95] [cursor=pointer]
                      - paragraph [ref=e96] [cursor=pointer]: time off
                  - img [ref=e97] [cursor=pointer]
              - button "👶 Parental Leave (Elternzeit) time off" [ref=e99] [cursor=pointer]:
                - generic [ref=e100] [cursor=pointer]:
                  - generic [ref=e101] [cursor=pointer]:
                    - generic [ref=e102] [cursor=pointer]: 👶
                    - generic [ref=e103] [cursor=pointer]:
                      - heading "Parental Leave (Elternzeit)" [level=4] [ref=e104] [cursor=pointer]
                      - paragraph [ref=e105] [cursor=pointer]: time off
                  - img [ref=e106] [cursor=pointer]
              - button "🎒 Sabbatical Leave (Sabbatjahr) time off" [ref=e108] [cursor=pointer]:
                - generic [ref=e109] [cursor=pointer]:
                  - generic [ref=e110] [cursor=pointer]:
                    - generic [ref=e111] [cursor=pointer]: 🎒
                    - generic [ref=e112] [cursor=pointer]:
                      - heading "Sabbatical Leave (Sabbatjahr)" [level=4] [ref=e113] [cursor=pointer]
                      - paragraph [ref=e114] [cursor=pointer]: time off
                  - img [ref=e115] [cursor=pointer]
              - button "📅 Special Leave Days (Sonderurlaub) time off" [ref=e117] [cursor=pointer]:
                - generic [ref=e118] [cursor=pointer]:
                  - generic [ref=e119] [cursor=pointer]:
                    - generic [ref=e120] [cursor=pointer]: 📅
                    - generic [ref=e121] [cursor=pointer]:
                      - heading "Special Leave Days (Sonderurlaub)" [level=4] [ref=e122] [cursor=pointer]
                      - paragraph [ref=e123] [cursor=pointer]: time off
                  - img [ref=e124] [cursor=pointer]
              - button "🏖️ Unlimited PTO time off" [ref=e126] [cursor=pointer]:
                - generic [ref=e127] [cursor=pointer]:
                  - generic [ref=e128] [cursor=pointer]:
                    - generic [ref=e129] [cursor=pointer]: 🏖️
                    - generic [ref=e130] [cursor=pointer]:
                      - heading "Unlimited PTO" [level=4] [ref=e131] [cursor=pointer]
                      - paragraph [ref=e132] [cursor=pointer]: time off
                  - img [ref=e133] [cursor=pointer]
          - generic [ref=e135]:
            - heading "Financial" [level=3] [ref=e136]
            - generic [ref=e137]:
              - button "💎 Capital-Forming Benefits (Vermögenswirksame Leistungen) financial" [ref=e138] [cursor=pointer]:
                - generic [ref=e139] [cursor=pointer]:
                  - generic [ref=e140] [cursor=pointer]:
                    - generic [ref=e141] [cursor=pointer]: 💎
                    - generic [ref=e142] [cursor=pointer]:
                      - heading "Capital-Forming Benefits (Vermögenswirksame Leistungen)" [level=4] [ref=e143] [cursor=pointer]
                      - paragraph [ref=e144] [cursor=pointer]: financial
                  - img [ref=e145] [cursor=pointer]
              - button "🎄 Christmas Bonus (Weihnachtsgeld) financial 13th month salary typically paid in November/December" [ref=e147] [cursor=pointer]:
                - generic [ref=e148] [cursor=pointer]:
                  - generic [ref=e149] [cursor=pointer]:
                    - generic [ref=e150] [cursor=pointer]: 🎄
                    - generic [ref=e151] [cursor=pointer]:
                      - heading "Christmas Bonus (Weihnachtsgeld)" [level=4] [ref=e152] [cursor=pointer]
                      - paragraph [ref=e153] [cursor=pointer]: financial
                      - paragraph [ref=e154] [cursor=pointer]: 13th month salary typically paid in November/December
                  - img [ref=e155] [cursor=pointer]
              - button "💰 Company Pension Scheme (Betriebliche Altersvorsorge) financial" [ref=e157] [cursor=pointer]:
                - generic [ref=e158] [cursor=pointer]:
                  - generic [ref=e159] [cursor=pointer]:
                    - generic [ref=e160] [cursor=pointer]: 💰
                    - generic [ref=e161] [cursor=pointer]:
                      - heading "Company Pension Scheme (Betriebliche Altersvorsorge)" [level=4] [ref=e162] [cursor=pointer]
                      - paragraph [ref=e163] [cursor=pointer]: financial
                  - img [ref=e164] [cursor=pointer]
              - button "🛍️ Corporate Benefits financial" [ref=e166] [cursor=pointer]:
                - generic [ref=e167] [cursor=pointer]:
                  - generic [ref=e168] [cursor=pointer]:
                    - generic [ref=e169] [cursor=pointer]: 🛍️
                    - generic [ref=e170] [cursor=pointer]:
                      - heading "Corporate Benefits" [level=4] [ref=e171] [cursor=pointer]
                      - paragraph [ref=e172] [cursor=pointer]: financial
                  - img [ref=e173] [cursor=pointer]
              - button "📊 Employee Stock Purchase Plan (Mitarbeiterbeteiligung) financial" [ref=e175] [cursor=pointer]:
                - generic [ref=e176] [cursor=pointer]:
                  - generic [ref=e177] [cursor=pointer]:
                    - generic [ref=e178] [cursor=pointer]: 📊
                    - generic [ref=e179] [cursor=pointer]:
                      - heading "Employee Stock Purchase Plan (Mitarbeiterbeteiligung)" [level=4] [ref=e180] [cursor=pointer]
                      - paragraph [ref=e181] [cursor=pointer]: financial
                  - img [ref=e182] [cursor=pointer]
              - button "🏖️ Holiday Bonus (Urlaubsgeld) financial" [ref=e184] [cursor=pointer]:
                - generic [ref=e185] [cursor=pointer]:
                  - generic [ref=e186] [cursor=pointer]:
                    - generic [ref=e187] [cursor=pointer]: 🏖️
                    - generic [ref=e188] [cursor=pointer]:
                      - heading "Holiday Bonus (Urlaubsgeld)" [level=4] [ref=e189] [cursor=pointer]
                      - paragraph [ref=e190] [cursor=pointer]: financial
                  - img [ref=e191] [cursor=pointer]
              - button "📈 Profit Sharing (Gewinnbeteiligung) financial" [ref=e193] [cursor=pointer]:
                - generic [ref=e194] [cursor=pointer]:
                  - generic [ref=e195] [cursor=pointer]:
                    - generic [ref=e196] [cursor=pointer]: 📈
                    - generic [ref=e197] [cursor=pointer]:
                      - heading "Profit Sharing (Gewinnbeteiligung)" [level=4] [ref=e198] [cursor=pointer]
                      - paragraph [ref=e199] [cursor=pointer]: financial
                  - img [ref=e200] [cursor=pointer]
              - button "💰 Retirement Plan financial" [ref=e202] [cursor=pointer]:
                - generic [ref=e203] [cursor=pointer]:
                  - generic [ref=e204] [cursor=pointer]:
                    - generic [ref=e205] [cursor=pointer]: 💰
                    - generic [ref=e206] [cursor=pointer]:
                      - heading "Retirement Plan" [level=4] [ref=e207] [cursor=pointer]
                      - paragraph [ref=e208] [cursor=pointer]: financial
                  - img [ref=e209] [cursor=pointer]
              - button "📈 Stock Options financial" [ref=e211] [cursor=pointer]:
                - generic [ref=e212] [cursor=pointer]:
                  - generic [ref=e213] [cursor=pointer]:
                    - generic [ref=e214] [cursor=pointer]: 📈
                    - generic [ref=e215] [cursor=pointer]:
                      - heading "Stock Options" [level=4] [ref=e216] [cursor=pointer]
                      - paragraph [ref=e217] [cursor=pointer]: financial
                  - img [ref=e218] [cursor=pointer]
          - generic [ref=e220]:
            - heading "Development" [level=3] [ref=e221]
            - generic [ref=e222]:
              - button "🎤 Conference Attendance (Konferenz-Teilnahme) development" [ref=e223] [cursor=pointer]:
                - generic [ref=e224] [cursor=pointer]:
                  - generic [ref=e225] [cursor=pointer]:
                    - generic [ref=e226] [cursor=pointer]: 🎤
                    - generic [ref=e227] [cursor=pointer]:
                      - heading "Conference Attendance (Konferenz-Teilnahme)" [level=4] [ref=e228] [cursor=pointer]
                      - paragraph [ref=e229] [cursor=pointer]: development
                  - img [ref=e230] [cursor=pointer]
              - button "🎓 Internal Training Programs (Interne Schulungen) development" [ref=e232] [cursor=pointer]:
                - generic [ref=e233] [cursor=pointer]:
                  - generic [ref=e234] [cursor=pointer]:
                    - generic [ref=e235] [cursor=pointer]: 🎓
                    - generic [ref=e236] [cursor=pointer]:
                      - heading "Internal Training Programs (Interne Schulungen)" [level=4] [ref=e237] [cursor=pointer]
                      - paragraph [ref=e238] [cursor=pointer]: development
                  - img [ref=e239] [cursor=pointer]
              - button "🗣️ Language Learning Support (Sprachkurse) development" [ref=e241] [cursor=pointer]:
                - generic [ref=e242] [cursor=pointer]:
                  - generic [ref=e243] [cursor=pointer]:
                    - generic [ref=e244] [cursor=pointer]: 🗣️
                    - generic [ref=e245] [cursor=pointer]:
                      - heading "Language Learning Support (Sprachkurse)" [level=4] [ref=e246] [cursor=pointer]
                      - paragraph [ref=e247] [cursor=pointer]: development
                  - img [ref=e248] [cursor=pointer]
              - button "📚 Learning Budget development" [ref=e250] [cursor=pointer]:
                - generic [ref=e251] [cursor=pointer]:
                  - generic [ref=e252] [cursor=pointer]:
                    - generic [ref=e253] [cursor=pointer]: 📚
                    - generic [ref=e254] [cursor=pointer]:
                      - heading "Learning Budget" [level=4] [ref=e255] [cursor=pointer]
                      - paragraph [ref=e256] [cursor=pointer]: development
                  - img [ref=e257] [cursor=pointer]
              - button "👨‍🏫 Mentoring Programs (Mentoring-Programme) development" [ref=e259] [cursor=pointer]:
                - generic [ref=e260] [cursor=pointer]:
                  - generic [ref=e261] [cursor=pointer]:
                    - generic [ref=e262] [cursor=pointer]: 👨‍🏫
                    - generic [ref=e263] [cursor=pointer]:
                      - heading "Mentoring Programs (Mentoring-Programme)" [level=4] [ref=e264] [cursor=pointer]
                      - paragraph [ref=e265] [cursor=pointer]: development
                  - img [ref=e266] [cursor=pointer]
              - button "📖 Study Leave (Bildungsurlaub) development" [ref=e268] [cursor=pointer]:
                - generic [ref=e269] [cursor=pointer]:
                  - generic [ref=e270] [cursor=pointer]:
                    - generic [ref=e271] [cursor=pointer]: 📖
                    - generic [ref=e272] [cursor=pointer]:
                      - heading "Study Leave (Bildungsurlaub)" [level=4] [ref=e273] [cursor=pointer]
                      - paragraph [ref=e274] [cursor=pointer]: development
                  - img [ref=e275] [cursor=pointer]
              - button "📚 Training Budget (Weiterbildungsbudget) development" [ref=e277] [cursor=pointer]:
                - generic [ref=e278] [cursor=pointer]:
                  - generic [ref=e279] [cursor=pointer]:
                    - generic [ref=e280] [cursor=pointer]: 📚
                    - generic [ref=e281] [cursor=pointer]:
                      - heading "Training Budget (Weiterbildungsbudget)" [level=4] [ref=e282] [cursor=pointer]
                      - paragraph [ref=e283] [cursor=pointer]: development
                  - img [ref=e284] [cursor=pointer]
          - generic [ref=e286]:
            - heading "Fitness" [level=3] [ref=e287]
            - generic [ref=e288]:
              - button "🚲 Bike Leasing (Dienstfahrrad) - Bikeleasing fitness" [ref=e289] [cursor=pointer]:
                - generic [ref=e290] [cursor=pointer]:
                  - generic [ref=e291] [cursor=pointer]:
                    - generic [ref=e292] [cursor=pointer]: 🚲
                    - generic [ref=e293] [cursor=pointer]:
                      - heading "Bike Leasing (Dienstfahrrad) - Bikeleasing" [level=4] [ref=e294] [cursor=pointer]
                      - paragraph [ref=e295] [cursor=pointer]: fitness
                  - img [ref=e296] [cursor=pointer]
              - button "🚲 Bike Leasing (Dienstfahrrad) - Deutsche Dienstrad fitness" [ref=e298] [cursor=pointer]:
                - generic [ref=e299] [cursor=pointer]:
                  - generic [ref=e300] [cursor=pointer]:
                    - generic [ref=e301] [cursor=pointer]: 🚲
                    - generic [ref=e302] [cursor=pointer]:
                      - heading "Bike Leasing (Dienstfahrrad) - Deutsche Dienstrad" [level=4] [ref=e303] [cursor=pointer]
                      - paragraph [ref=e304] [cursor=pointer]: fitness
                  - img [ref=e305] [cursor=pointer]
              - button "🚲 Bike Leasing (Dienstfahrrad) - JobRad fitness" [ref=e307] [cursor=pointer]:
                - generic [ref=e308] [cursor=pointer]:
                  - generic [ref=e309] [cursor=pointer]:
                    - generic [ref=e310] [cursor=pointer]: 🚲
                    - generic [ref=e311] [cursor=pointer]:
                      - heading "Bike Leasing (Dienstfahrrad) - JobRad" [level=4] [ref=e312] [cursor=pointer]
                      - paragraph [ref=e313] [cursor=pointer]: fitness
                  - img [ref=e314] [cursor=pointer]
              - button "🚲 Bike Leasing (Dienstfahrrad) - Other fitness" [ref=e316] [cursor=pointer]:
                - generic [ref=e317] [cursor=pointer]:
                  - generic [ref=e318] [cursor=pointer]:
                    - generic [ref=e319] [cursor=pointer]: 🚲
                    - generic [ref=e320] [cursor=pointer]:
                      - heading "Bike Leasing (Dienstfahrrad) - Other" [level=4] [ref=e321] [cursor=pointer]
                      - paragraph [ref=e322] [cursor=pointer]: fitness
                  - img [ref=e323] [cursor=pointer]
              - button "⚽ Company Sports Teams (Betriebssport) fitness" [ref=e325] [cursor=pointer]:
                - generic [ref=e326] [cursor=pointer]:
                  - generic [ref=e327] [cursor=pointer]:
                    - generic [ref=e328] [cursor=pointer]: ⚽
                    - generic [ref=e329] [cursor=pointer]:
                      - heading "Company Sports Teams (Betriebssport)" [level=4] [ref=e330] [cursor=pointer]
                      - paragraph [ref=e331] [cursor=pointer]: fitness
                  - img [ref=e332] [cursor=pointer]
              - button "🪑 Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz) fitness" [ref=e334] [cursor=pointer]:
                - generic [ref=e335] [cursor=pointer]:
                  - generic [ref=e336] [cursor=pointer]:
                    - generic [ref=e337] [cursor=pointer]: 🪑
                    - generic [ref=e338] [cursor=pointer]:
                      - heading "Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)" [level=4] [ref=e339] [cursor=pointer]
                      - paragraph [ref=e340] [cursor=pointer]: fitness
                  - img [ref=e341] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - EGYM Wellpass fitness" [ref=e343] [cursor=pointer]:
                - generic [ref=e344] [cursor=pointer]:
                  - generic [ref=e345] [cursor=pointer]:
                    - generic [ref=e346] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e347] [cursor=pointer]:
                      - heading "Gym Membership - EGYM Wellpass" [level=4] [ref=e348] [cursor=pointer]
                      - paragraph [ref=e349] [cursor=pointer]: fitness
                  - img [ref=e350] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - Hansefit fitness" [ref=e352] [cursor=pointer]:
                - generic [ref=e353] [cursor=pointer]:
                  - generic [ref=e354] [cursor=pointer]:
                    - generic [ref=e355] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e356] [cursor=pointer]:
                      - heading "Gym Membership - Hansefit" [level=4] [ref=e357] [cursor=pointer]
                      - paragraph [ref=e358] [cursor=pointer]: fitness
                  - img [ref=e359] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - Other fitness" [ref=e361] [cursor=pointer]:
                - generic [ref=e362] [cursor=pointer]:
                  - generic [ref=e363] [cursor=pointer]:
                    - generic [ref=e364] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e365] [cursor=pointer]:
                      - heading "Gym Membership - Other" [level=4] [ref=e366] [cursor=pointer]
                      - paragraph [ref=e367] [cursor=pointer]: fitness
                  - img [ref=e368] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - Urban Sports Club fitness" [ref=e370] [cursor=pointer]:
                - generic [ref=e371] [cursor=pointer]:
                  - generic [ref=e372] [cursor=pointer]:
                    - generic [ref=e373] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e374] [cursor=pointer]:
                      - heading "Gym Membership - Urban Sports Club" [level=4] [ref=e375] [cursor=pointer]
                      - paragraph [ref=e376] [cursor=pointer]: fitness
                  - img [ref=e377] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - Wellhub fitness" [ref=e379] [cursor=pointer]:
                - generic [ref=e380] [cursor=pointer]:
                  - generic [ref=e381] [cursor=pointer]:
                    - generic [ref=e382] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e383] [cursor=pointer]:
                      - heading "Gym Membership - Wellhub" [level=4] [ref=e384] [cursor=pointer]
                      - paragraph [ref=e385] [cursor=pointer]: fitness
                  - img [ref=e386] [cursor=pointer]
              - button "💆‍♀️ Massage Services (Massagen am Arbeitsplatz) fitness" [ref=e388] [cursor=pointer]:
                - generic [ref=e389] [cursor=pointer]:
                  - generic [ref=e390] [cursor=pointer]:
                    - generic [ref=e391] [cursor=pointer]: 💆‍♀️
                    - generic [ref=e392] [cursor=pointer]:
                      - heading "Massage Services (Massagen am Arbeitsplatz)" [level=4] [ref=e393] [cursor=pointer]
                      - paragraph [ref=e394] [cursor=pointer]: fitness
                  - img [ref=e395] [cursor=pointer]
          - generic [ref=e397]:
            - heading "Work-Life Balance" [level=3] [ref=e398]
            - generic [ref=e399]:
              - button "⏰ Flexible Working Hours (Gleitzeit) work life" [ref=e400] [cursor=pointer]:
                - generic [ref=e401] [cursor=pointer]:
                  - generic [ref=e402] [cursor=pointer]:
                    - generic [ref=e403] [cursor=pointer]: ⏰
                    - generic [ref=e404] [cursor=pointer]:
                      - heading "Flexible Working Hours (Gleitzeit)" [level=4] [ref=e405] [cursor=pointer]
                      - paragraph [ref=e406] [cursor=pointer]: work life
                  - img [ref=e407] [cursor=pointer]
              - button "👥 Job Sharing (Arbeitsplatz-Teilung) work life" [ref=e409] [cursor=pointer]:
                - generic [ref=e410] [cursor=pointer]:
                  - generic [ref=e411] [cursor=pointer]:
                    - generic [ref=e412] [cursor=pointer]: 👥
                    - generic [ref=e413] [cursor=pointer]:
                      - heading "Job Sharing (Arbeitsplatz-Teilung)" [level=4] [ref=e414] [cursor=pointer]
                      - paragraph [ref=e415] [cursor=pointer]: work life
                  - img [ref=e416] [cursor=pointer]
              - button "⏱️ Part-Time Work Options (Teilzeitarbeit) work life" [ref=e418] [cursor=pointer]:
                - generic [ref=e419] [cursor=pointer]:
                  - generic [ref=e420] [cursor=pointer]:
                    - generic [ref=e421] [cursor=pointer]: ⏱️
                    - generic [ref=e422] [cursor=pointer]:
                      - heading "Part-Time Work Options (Teilzeitarbeit)" [level=4] [ref=e423] [cursor=pointer]
                      - paragraph [ref=e424] [cursor=pointer]: work life
                  - img [ref=e425] [cursor=pointer]
              - button "🏠 Remote Work (Homeoffice) work life" [ref=e427] [cursor=pointer]:
                - generic [ref=e428] [cursor=pointer]:
                  - generic [ref=e429] [cursor=pointer]:
                    - generic [ref=e430] [cursor=pointer]: 🏠
                    - generic [ref=e431] [cursor=pointer]:
                      - heading "Remote Work (Homeoffice)" [level=4] [ref=e432] [cursor=pointer]
                      - paragraph [ref=e433] [cursor=pointer]: work life
                  - img [ref=e434] [cursor=pointer]
              - button "📅 Viertage Woche (4-Day Work Week) work life Four-day work week with full salary - modern work-life balance approach" [ref=e436] [cursor=pointer]:
                - generic [ref=e437] [cursor=pointer]:
                  - generic [ref=e438] [cursor=pointer]:
                    - generic [ref=e439] [cursor=pointer]: 📅
                    - generic [ref=e440] [cursor=pointer]:
                      - heading "Viertage Woche (4-Day Work Week)" [level=4] [ref=e441] [cursor=pointer]
                      - paragraph [ref=e442] [cursor=pointer]: work life
                      - paragraph [ref=e443] [cursor=pointer]: Four-day work week with full salary - modern work-life balance approach
                  - img [ref=e444] [cursor=pointer]
          - generic [ref=e446]:
            - heading "Other" [level=3] [ref=e447]
            - generic [ref=e448]:
              - button "🥗 Catered Meals (Catering-Service) other" [ref=e449] [cursor=pointer]:
                - generic [ref=e450] [cursor=pointer]:
                  - generic [ref=e451] [cursor=pointer]:
                    - generic [ref=e452] [cursor=pointer]: 🥗
                    - generic [ref=e453] [cursor=pointer]:
                      - heading "Catered Meals (Catering-Service)" [level=4] [ref=e454] [cursor=pointer]
                      - paragraph [ref=e455] [cursor=pointer]: other
                  - img [ref=e456] [cursor=pointer]
              - button "👨‍👩‍👧‍👦 Childcare Support (Kinderbetreuung) other" [ref=e458] [cursor=pointer]:
                - generic [ref=e459] [cursor=pointer]:
                  - generic [ref=e460] [cursor=pointer]:
                    - generic [ref=e461] [cursor=pointer]: 👨‍👩‍👧‍👦
                    - generic [ref=e462] [cursor=pointer]:
                      - heading "Childcare Support (Kinderbetreuung)" [level=4] [ref=e463] [cursor=pointer]
                      - paragraph [ref=e464] [cursor=pointer]: other
                  - img [ref=e465] [cursor=pointer]
              - button "🍱 Company Cafeteria (Betriebskantine) other" [ref=e467] [cursor=pointer]:
                - generic [ref=e468] [cursor=pointer]:
                  - generic [ref=e469] [cursor=pointer]:
                    - generic [ref=e470] [cursor=pointer]: 🍱
                    - generic [ref=e471] [cursor=pointer]:
                      - heading "Company Cafeteria (Betriebskantine)" [level=4] [ref=e472] [cursor=pointer]
                      - paragraph [ref=e473] [cursor=pointer]: other
                  - img [ref=e474] [cursor=pointer]
              - button "🚗 Company Car (Dienstwagen) other" [ref=e476] [cursor=pointer]:
                - generic [ref=e477] [cursor=pointer]:
                  - generic [ref=e478] [cursor=pointer]:
                    - generic [ref=e479] [cursor=pointer]: 🚗
                    - generic [ref=e480] [cursor=pointer]:
                      - heading "Company Car (Dienstwagen)" [level=4] [ref=e481] [cursor=pointer]
                      - paragraph [ref=e482] [cursor=pointer]: other
                  - img [ref=e483] [cursor=pointer]
              - button "🆘 Emergency Childcare (Notfall-Kinderbetreuung) other" [ref=e485] [cursor=pointer]:
                - generic [ref=e486] [cursor=pointer]:
                  - generic [ref=e487] [cursor=pointer]:
                    - generic [ref=e488] [cursor=pointer]: 🆘
                    - generic [ref=e489] [cursor=pointer]:
                      - heading "Emergency Childcare (Notfall-Kinderbetreuung)" [level=4] [ref=e490] [cursor=pointer]
                      - paragraph [ref=e491] [cursor=pointer]: other
                  - img [ref=e492] [cursor=pointer]
              - button "🎉 Family Events (Familienfeste) other" [ref=e494] [cursor=pointer]:
                - generic [ref=e495] [cursor=pointer]:
                  - generic [ref=e496] [cursor=pointer]:
                    - generic [ref=e497] [cursor=pointer]: 🎉
                    - generic [ref=e498] [cursor=pointer]:
                      - heading "Family Events (Familienfeste)" [level=4] [ref=e499] [cursor=pointer]
                      - paragraph [ref=e500] [cursor=pointer]: other
                  - img [ref=e501] [cursor=pointer]
              - button "☕ Free Coffee & Snacks (Kostenlose Verpflegung) other" [ref=e503] [cursor=pointer]:
                - generic [ref=e504] [cursor=pointer]:
                  - generic [ref=e505] [cursor=pointer]:
                    - generic [ref=e506] [cursor=pointer]: ☕
                    - generic [ref=e507] [cursor=pointer]:
                      - heading "Free Coffee & Snacks (Kostenlose Verpflegung)" [level=4] [ref=e508] [cursor=pointer]
                      - paragraph [ref=e509] [cursor=pointer]: other
                  - img [ref=e510] [cursor=pointer]
              - button "🍽️ Free Lunch other" [ref=e512] [cursor=pointer]:
                - generic [ref=e513] [cursor=pointer]:
                  - generic [ref=e514] [cursor=pointer]:
                    - generic [ref=e515] [cursor=pointer]: 🍽️
                    - generic [ref=e516] [cursor=pointer]:
                      - heading "Free Lunch" [level=4] [ref=e517] [cursor=pointer]
                      - paragraph [ref=e518] [cursor=pointer]: other
                  - img [ref=e519] [cursor=pointer]
              - button "🏋️‍♀️ Gym Membership - ClassPass other" [ref=e521] [cursor=pointer]:
                - generic [ref=e522] [cursor=pointer]:
                  - generic [ref=e523] [cursor=pointer]:
                    - generic [ref=e524] [cursor=pointer]: 🏋️‍♀️
                    - generic [ref=e525] [cursor=pointer]:
                      - heading "Gym Membership - ClassPass" [level=4] [ref=e526] [cursor=pointer]
                      - paragraph [ref=e527] [cursor=pointer]: other
                  - img [ref=e528] [cursor=pointer]
              - button "💻 Home Office Equipment (Homeoffice-Ausstattung) other" [ref=e530] [cursor=pointer]:
                - generic [ref=e531] [cursor=pointer]:
                  - generic [ref=e532] [cursor=pointer]:
                    - generic [ref=e533] [cursor=pointer]: 💻
                    - generic [ref=e534] [cursor=pointer]:
                      - heading "Home Office Equipment (Homeoffice-Ausstattung)" [level=4] [ref=e535] [cursor=pointer]
                      - paragraph [ref=e536] [cursor=pointer]: other
                  - img [ref=e537] [cursor=pointer]
              - button "🌐 Internet Allowance (Internet-Zuschuss) other" [ref=e539] [cursor=pointer]:
                - generic [ref=e540] [cursor=pointer]:
                  - generic [ref=e541] [cursor=pointer]:
                    - generic [ref=e542] [cursor=pointer]: 🌐
                    - generic [ref=e543] [cursor=pointer]:
                      - heading "Internet Allowance (Internet-Zuschuss)" [level=4] [ref=e544] [cursor=pointer]
                      - paragraph [ref=e545] [cursor=pointer]: other
                  - img [ref=e546] [cursor=pointer]
              - button "🚊 Job Ticket (Jobticket) other Subsidized public transportation pass" [ref=e548] [cursor=pointer]:
                - generic [ref=e549] [cursor=pointer]:
                  - generic [ref=e550] [cursor=pointer]:
                    - generic [ref=e551] [cursor=pointer]: 🚊
                    - generic [ref=e552] [cursor=pointer]:
                      - heading "Job Ticket (Jobticket)" [level=4] [ref=e553] [cursor=pointer]
                      - paragraph [ref=e554] [cursor=pointer]: other
                      - paragraph [ref=e555] [cursor=pointer]: Subsidized public transportation pass
                  - img [ref=e556] [cursor=pointer]
              - button "🍽️ Meal Vouchers (Essensgutscheine) other" [ref=e558] [cursor=pointer]:
                - generic [ref=e559] [cursor=pointer]:
                  - generic [ref=e560] [cursor=pointer]:
                    - generic [ref=e561] [cursor=pointer]: 🍽️
                    - generic [ref=e562] [cursor=pointer]:
                      - heading "Meal Vouchers (Essensgutscheine)" [level=4] [ref=e563] [cursor=pointer]
                      - paragraph [ref=e564] [cursor=pointer]: other
                  - img [ref=e565] [cursor=pointer]
              - button "📱 Mobile Phone Allowance (Handy-Zuschuss) other" [ref=e567] [cursor=pointer]:
                - generic [ref=e568] [cursor=pointer]:
                  - generic [ref=e569] [cursor=pointer]:
                    - generic [ref=e570] [cursor=pointer]: 📱
                    - generic [ref=e571] [cursor=pointer]:
                      - heading "Mobile Phone Allowance (Handy-Zuschuss)" [level=4] [ref=e572] [cursor=pointer]
                      - paragraph [ref=e573] [cursor=pointer]: other
                  - img [ref=e574] [cursor=pointer]
              - button "🅿️ Parking Allowance (Parkplatz-Zuschuss) other" [ref=e576] [cursor=pointer]:
                - generic [ref=e577] [cursor=pointer]:
                  - generic [ref=e578] [cursor=pointer]:
                    - generic [ref=e579] [cursor=pointer]: 🅿️
                    - generic [ref=e580] [cursor=pointer]:
                      - heading "Parking Allowance (Parkplatz-Zuschuss)" [level=4] [ref=e581] [cursor=pointer]
                      - paragraph [ref=e582] [cursor=pointer]: other
                  - img [ref=e583] [cursor=pointer]
              - button "🐕 Pet-Friendly Office other" [ref=e585] [cursor=pointer]:
                - generic [ref=e586] [cursor=pointer]:
                  - generic [ref=e587] [cursor=pointer]:
                    - generic [ref=e588] [cursor=pointer]: 🐕
                    - generic [ref=e589] [cursor=pointer]:
                      - heading "Pet-Friendly Office" [level=4] [ref=e590] [cursor=pointer]
                      - paragraph [ref=e591] [cursor=pointer]: other
                  - img [ref=e592] [cursor=pointer]
              - button "✈️ Travel Allowance (Reisekostenzuschuss) other" [ref=e594] [cursor=pointer]:
                - generic [ref=e595] [cursor=pointer]:
                  - generic [ref=e596] [cursor=pointer]:
                    - generic [ref=e597] [cursor=pointer]: ✈️
                    - generic [ref=e598] [cursor=pointer]:
                      - heading "Travel Allowance (Reisekostenzuschuss)" [level=4] [ref=e599] [cursor=pointer]
                      - paragraph [ref=e600] [cursor=pointer]: other
                  - img [ref=e601] [cursor=pointer]
  - button "Open Next.js Dev Tools" [ref=e608] [cursor=pointer]:
    - img [ref=e609] [cursor=pointer]
  - alert [ref=e612]
```